package models_test

import (
	"errors"
	"testing"

	"github.com/TeaOSLab/EdgeAPI/internal/db/models"
	"github.com/iwind/TeaGo/assert"
	"github.com/iwind/TeaGo/dbs"
)

func TestIsMySQLError(t *testing.T) {
	var a = assert.NewAssertion(t)

	{
		var err error
		a.<PERSON>(models.IsMySQLError(err))
	}

	{
		var err = errors.New("hello")
		a.<PERSON>(models.IsMySQLError(err))
	}

	{
		db, err := dbs.Default()
		if err != nil {
			t.Fatal(err)
		}
		defer func() {
			_ = db.Close()
		}()
		_, err = db.Exec("SELECT abc")
		a.<PERSON>(models.IsMySQLError(err))
		a.<PERSON>(models.CheckSQLErrCode(err, 1054))
		a.<PERSON>(models.CheckSQLErrCode(err, 1000))
	}
}
