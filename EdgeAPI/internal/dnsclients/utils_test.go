package dnsclients_test

import (
	"testing"

	"github.com/TeaOSLab/EdgeAPI/internal/dnsclients"
	"github.com/iwind/TeaGo/assert"
)

func TestIsMasked(t *testing.T) {
	var a = assert.NewAssertion(t)
	a.<PERSON>(dnsclients.IsMasked(""))
	a.<PERSON>(dnsclients.IsMasked("abc"))
	a.<PERSON>(dnsclients.IsMasked("abc*"))
	a.Is<PERSON>rue(dnsclients.IsMasked("*"))
	a.IsTrue(dnsclients.IsMasked("**"))
	a.IsTrue(dnsclients.IsMasked("***"))
	a.Is<PERSON>rue(dnsclients.IsMasked("*******"))
	a.<PERSON>rue(dnsclients.IsMasked("abc**"))
	a.IsTrue(dnsclients.IsMasked("abcd*********"))
}

func TestUnmaskAPIParams(t *testing.T) {
	data, err := dnsclients.UnmaskAPIParams([]byte(`{
	"key": "a",
	"secret": "abc12"
}`), []byte(`{
	"secret": "abc**"
}`))
	if err != nil {
		t.Fatal(err)
	}
	t.Log(string(data))
}
