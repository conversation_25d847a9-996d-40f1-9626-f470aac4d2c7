# EdgeOpenAPI

EdgeOpenAPI is an enterprise-grade API gateway for EdgeAPI, providing RESTful HTTP interfaces for enterprise customers to access EdgeAPI functionality.

## Overview

EdgeOpenAPI serves as a "glue layer" between enterprise clients and EdgeAPI, offering:

- **Protocol Conversion**: Converts EdgeAPI's gRPC interfaces to RESTful HTTP APIs
- **Enterprise Integration**: Provides enterprise-friendly API interfaces
- **Authentication**: Integrates with EdgeAPI's existing authentication system
- **Zero Business Logic**: Completely reuses EdgeAPI functionality for data consistency

## Architecture

```
Enterprise Client → EdgeOpenAPI (Protocol Conversion) → EdgeAPI (Business Logic) → Database
```

## Features

### Supported API Modules

| Module | Description | EdgeAPI Service | Status |
|--------|-------------|-----------------|---------|
| Authentication | 用户登录和令牌验证 | UserService, APIAccessTokenService | ✅ 已实现 |
| User Management | 企业子用户 CRUD 操作 | UserService | ✅ 已实现 |
| Cluster Management | 企业集群创建和管理 | NodeClusterService | 🚧 计划中 |
| Node Management | 节点使用权购买和管理 | NodeService | 🚧 计划中 |
| CDN Services | 完整的 CDN 服务生命周期管理 | ServerService | 🚧 计划中 |
| Statistics | 企业级数据分析 | 特定统计服务 | 🚧 计划中 |
| Billing | 企业计费和配额管理 | UserBillService | 🚧 计划中 |

### Authentication

EdgeOpenAPI 支持两种认证方式：

#### 1. 用户名/密码登录
```bash
# 用户登录获取认证信息
curl -X POST "http://localhost:8080/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "your_username",
    "password": "your_password"
  }'
```

#### 2. API 密钥认证
使用 EdgeAPI 的 AccessKey/AccessToken 机制：

```http
X-API-Key-ID: <ACCESS_KEY_ID>
X-API-Key: <ACCESS_KEY>
Content-Type: application/json
```

## Quick Start

### Prerequisites

- Go 1.19+
- EdgeAPI service running
- Docker (optional)

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd EdgeOpenAPI

# Install dependencies
go mod tidy

# Copy and configure settings
cp configs/config.example.yaml configs/config.yaml
# Edit config.yaml to configure EdgeAPI connection

# Run the service
go run cmd/server/main.go
```

### API Usage Examples

#### 用户认证

```bash
# 用户登录
curl -X POST "http://localhost:8080/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "enterprise_user",
    "password": "secure_password"
  }'

# 验证令牌
curl -X POST "http://localhost:8080/api/v1/auth/validate" \
  -H "X-API-Key-ID: your_key_id" \
  -H "X-API-Key: your_access_key"
```

#### 用户管理

```bash
# 获取用户列表
curl -X GET "http://localhost:8080/api/v1/users" \
  -H "X-API-Key-ID: your_key_id" \
  -H "X-API-Key: your_access_key"

# 创建用户
curl -X POST "http://localhost:8080/api/v1/users" \
  -H "X-API-Key-ID: your_key_id" \
  -H "X-API-Key: your_access_key" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "enterprise_user",
    "password": "secure_password",
    "fullname": "Enterprise User",
    "email": "<EMAIL>"
  }'
```

## Development

### Project Structure

```
EdgeOpenAPI/
├── cmd/server/          # Service entry point
├── internal/
│   ├── handlers/        # HTTP handlers
│   ├── middleware/      # Middleware
│   ├── grpc/           # gRPC client
│   ├── models/         # Data models
│   └── routes/         # Route definitions
├── pkg/
│   ├── converter/      # Format conversion
│   └── client/         # EdgeAPI client
├── configs/            # Configuration files
├── docs/              # Documentation
└── tests/             # Test files
```

### Adding New APIs

1. Add handler in `internal/handlers/`
2. Add format conversion logic in `pkg/converter/`
3. Register routes in `internal/routes/`
4. Write test cases

### Testing

```bash
# Run all tests
go test ./...

# Run specific module tests
go test ./internal/handlers/

# Generate test coverage report
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out
```

## API Documentation

Complete API documentation is available at:
- [API Specification](./docs/EdgeOpenAPI_API规范.md)
- Swagger UI: http://localhost:8080/swagger/index.html (when service is running)

## Deployment

### Docker Deployment

```bash
# Build image
docker build -t edgeopenapi .

# Run container
docker run -d \
  --name edgeopenapi \
  -p 8080:8080 \
  -v $(pwd)/configs:/app/configs \
  edgeopenapi
```

## License

This project is licensed under the same terms as EdgeAPI.

## Contributing

Please refer to the EdgeAPI project's contribution guidelines.
