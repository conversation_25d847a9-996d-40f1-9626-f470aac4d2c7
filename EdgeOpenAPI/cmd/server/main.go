package main

import (
	"context"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/TeaOSLab/EdgeOpenAPI/internal/grpc"
	"github.com/TeaOSLab/EdgeOpenAPI/internal/middleware"
	"github.com/TeaOSLab/EdgeOpenAPI/internal/routes"
	"github.com/gin-gonic/gin"
)

func main() {
	// Initialize EdgeAPI gRPC client
	edgeAPIClient, err := grpc.NewEdgeAPIClient("localhost:8001") // Default EdgeAPI address
	if err != nil {
		log.Fatalf("Failed to connect to EdgeAPI: %v", err)
	}
	defer edgeAPIClient.Close()

	// Initialize Gin router
	router := gin.Default()

	// Add global middleware
	router.Use(gin.Logger())
	router.Use(gin.Recovery())
	router.Use(middleware.CORS())

	// Add authentication middleware
	router.Use(middleware.AuthMiddleware(edgeAPIClient))

	// Setup routes
	routes.SetupRoutes(router, edgeAPIClient)

	// Create HTTP server
	server := &http.Server{
		Addr:    ":8080",
		Handler: router,
	}

	// Start server in a goroutine
	go func() {
		log.Println("Starting EdgeOpenAPI server on :8080")
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Println("Shutting down server...")

	// Give outstanding requests a deadline for completion
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		log.Fatalf("Server forced to shutdown: %v", err)
	}

	log.Println("Server exited")
}
