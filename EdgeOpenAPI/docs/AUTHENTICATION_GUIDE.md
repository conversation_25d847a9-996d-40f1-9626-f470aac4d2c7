# EdgeOpenAPI Authentication Guide

## Overview

EdgeOpenAPI provides user authentication endpoints that integrate with EdgeAPI's existing authentication system. The authentication flow supports username/password login and is compatible with the existing API key authentication system.

## Authentication Endpoints

### 1. User Login

**Endpoint**: `POST /api/v1/auth/login`

**Description**: Authenticates a user with username and password credentials.

**Request Body**:
```json
{
  "username": "<EMAIL>",
  "password": "secure_password"
}
```

**Success Response** (200 OK):
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "success": true,
    "message": "Login successful. Please create access keys for API access.",
    "user_id": 12345,
    "token_type": "Bearer"
  },
  "timestamp": 1640995200,
  "request_id": "req_abc12345"
}
```

**Error Response** (401 Unauthorized):
```json
{
  "code": 401,
  "message": "Login failed",
  "error": {
    "type": "authentication_error",
    "details": "请输入正确的用户名密码"
  },
  "timestamp": 1640995200,
  "request_id": "req_abc12345"
}
```

### 2. Token Validation

**Endpoint**: `POST /api/v1/auth/validate`

**Description**: Validates the current authentication token.

**Headers**:
```
X-API-Key-ID: <ACCESS_KEY_ID>
X-API-Key: <ACCESS_KEY>
```

**Success Response** (200 OK):
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "valid": true,
    "message": "Token is valid"
  },
  "timestamp": 1640995200,
  "request_id": "req_abc12345"
}
```

### 3. Access Key Management (Future Implementation)

**Note**: The following endpoints are planned for future implementation and currently return "not implemented" responses.

#### Create Access Key
**Endpoint**: `POST /api/v1/auth/access-keys`

#### List Access Keys
**Endpoint**: `GET /api/v1/auth/access-keys`

#### Delete Access Key
**Endpoint**: `DELETE /api/v1/auth/access-keys/{id}`

## Authentication Flow

### Current Implementation

1. **Login Process**:
   - User sends username/password to `/api/v1/auth/login`
   - EdgeOpenAPI validates credentials with EdgeAPI's `LoginUser` service
   - Returns success/failure response with user ID

2. **API Access**:
   - Users must create access keys through EdgeAPI admin interface
   - Use access keys with `X-API-Key-ID` and `X-API-Key` headers for API calls
   - EdgeOpenAPI validates access keys with EdgeAPI's `APIAccessTokenService`

### Integration with Existing System

EdgeOpenAPI maintains full compatibility with EdgeAPI's authentication system:

- **Username Support**: Supports username, email, or mobile number login
- **Password Validation**: Uses EdgeAPI's existing password validation
- **Access Keys**: Integrates with EdgeAPI's UserAccessKey system
- **Permissions**: Respects EdgeAPI's user permissions and data isolation

## Usage Examples

### 1. Login Example

```bash
curl -X POST "http://localhost:8080/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "enterprise_user",
    "password": "secure_password"
  }'
```

### 2. Validate Token Example

```bash
curl -X POST "http://localhost:8080/api/v1/auth/validate" \
  -H "X-API-Key-ID: your_key_id" \
  -H "X-API-Key: your_access_key"
```

### 3. Using API with Access Keys

```bash
curl -X GET "http://localhost:8080/api/v1/users" \
  -H "X-API-Key-ID: your_key_id" \
  -H "X-API-Key: your_access_key"
```

## Security Features

### 1. Input Validation
- Required field validation for username and password
- Proper error handling for malformed requests
- Standardized error response format

### 2. Authentication Bypass
- Login endpoint bypasses authentication middleware
- Health check endpoints remain unauthenticated
- All other endpoints require valid API keys

### 3. Error Handling
- Consistent error response format
- No sensitive information in error messages
- Request ID tracking for debugging

### 4. Rate Limiting Considerations
- Login endpoint should be protected with rate limiting in production
- Consider implementing account lockout after failed attempts
- Monitor for brute force attacks

## Future Enhancements

### 1. Access Key Management
- Complete implementation of access key CRUD operations
- Integration with EdgeAPI's UserAccessKeyService
- Proper user ID extraction from access tokens

### 2. Session Management
- Integration with EdgeAPI's LoginSessionService
- Session-based authentication option
- Remember me functionality

### 3. Enhanced Security
- Two-factor authentication support
- Password strength validation
- Login attempt monitoring

### 4. Token Management
- JWT token generation for stateless authentication
- Token refresh mechanism
- Token revocation support

## Error Codes

| Code | Description | Action |
|------|-------------|---------|
| 400 | Validation Error | Check request format and required fields |
| 401 | Authentication Failed | Verify credentials or access keys |
| 501 | Not Implemented | Feature not yet available |
| 500 | Internal Server Error | Check EdgeAPI connectivity |

## Testing

The authentication system includes comprehensive unit tests:

- Input validation testing
- Authentication flow testing
- Error handling verification
- Token validation testing

Run tests with:
```bash
go test ./internal/handlers/
```

## Architecture Compliance

The authentication implementation follows EdgeOpenAPI's core principles:

- ✅ **Zero Business Logic**: All authentication logic delegated to EdgeAPI
- ✅ **Protocol Conversion**: Pure HTTP-to-gRPC conversion
- ✅ **EdgeAPI Integration**: 100% compatible with existing system
- ✅ **Enterprise Standards**: Professional API design and security
