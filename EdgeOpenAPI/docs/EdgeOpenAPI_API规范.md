# EdgeOpenAPI接口规范

## 1. 接口概述

EdgeOpenAPI是EdgeAPI的企业级API网关，为企业客户提供RESTful HTTP接口，支持Enterprise Admin级别的功能权限。通过协议转换将EdgeAPI的gRPC服务暴露为标准的REST API。

### 1.1 基础信息
- **Base URL**: `https://api.example.com/v1`
- **协议**: HTTPS
- **数据格式**: JSON
- **字符编码**: UTF-8

### 1.2 认证方式
EdgeOpenAPI使用EdgeAPI的AccessKey/AccessToken认证机制：

```http
X-API-Key-ID: <ACCESS_KEY_ID>
X-API-Key: <ACCESS_KEY>
Content-Type: application/json
```

**认证流程**：
1. 企业客户使用AccessKey ID和AccessKey进行认证
2. EdgeOpenAPI调用EdgeAPI获取AccessToken
3. 后续请求使用AccessToken与EdgeAPI通信

### 1.3 响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": 1640995200,
  "request_id": "req_123456789"
}
```

### 1.4 错误响应
```json
{
  "code": 400,
  "message": "Invalid request parameters",
  "error": {
    "type": "validation_error",
    "details": [
      {
        "field": "username",
        "message": "Username is required"
      }
    ]
  },
  "timestamp": 1640995200,
  "request_id": "req_123456789"
}
```

## 2. 用户管理API

### 2.1 获取用户列表
```http
GET /api/v1/users
```

**请求参数**：
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| page | int | 否 | 页码，默认1 |
| size | int | 否 | 每页数量，默认20 |
| keyword | string | 否 | 搜索关键词 |
| status | string | 否 | 用户状态：active/inactive |

**响应示例**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "users": [
      {
        "id": 1001,
        "username": "enterprise_user_001",
        "fullname": "企业用户001",
        "email": "<EMAIL>",
        "mobile": "13800138001",
        "status": "active",
        "created_at": "2024-01-01T10:00:00Z",
        "updated_at": "2024-01-01T10:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 100,
      "pages": 5
    }
  }
}
```

### 2.2 创建用户
```http
POST /api/v1/users
```

**请求体**：
```json
{
  "username": "new_user",
  "password": "secure_password",
  "fullname": "新用户",
  "email": "<EMAIL>",
  "mobile": "13800138002",
  "remark": "备注信息"
}
```

**响应示例**：
```json
{
  "code": 200,
  "message": "User created successfully",
  "data": {
    "user_id": 1002,
    "username": "new_user"
  }
}
```

### 2.3 获取用户详情
```http
GET /api/v1/users/{id}
```

**路径参数**：
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| id | int | 是 | 用户ID |

**响应示例**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1001,
    "username": "enterprise_user_001",
    "fullname": "企业用户001",
    "email": "<EMAIL>",
    "mobile": "13800138001",
    "status": "active",
    "cluster_id": 100,
    "quota": {
      "bandwidth": "100Mbps",
      "storage": "1TB",
      "requests": 1000000
    },
    "created_at": "2024-01-01T10:00:00Z",
    "updated_at": "2024-01-01T10:00:00Z"
  }
}
```
