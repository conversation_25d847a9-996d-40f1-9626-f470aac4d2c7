# EdgeOpenAPI服务设计方案

## 1. 项目概述

EdgeOpenAPI是基于现有EdgeAPI B2B2C架构开发的企业级API网关服务，定位为EdgeAPI的"胶水层"，为企业客户提供Enterprise Admin级别的RESTful API接口。

### 1.1 核心定位
EdgeOpenAPI作为EdgeAPI的API网关，其核心价值在于：

1. **协议转换**：将EdgeAPI的gRPC接口转换为RESTful HTTP API
2. **企业级API封装**：为企业客户提供符合其使用习惯的API接口
3. **权限范围限制**：确保只暴露Enterprise Admin权限范围内的功能
4. **API文档和SDK**：提供完整的API文档和客户端SDK

### 1.2 设计原则
- **最大化复用**：完全依赖EdgeAPI现有的认证、权限、数据模型
- **最小化实现**：只实现协议转换和必要的业务逻辑封装
- **零数据库修改**：不修改任何现有数据库表结构
- **完全兼容**：确保与EdgeAPI的100%兼容性

### 1.3 技术选型
- **Web框架**: Gin (高性能HTTP框架)
- **通信协议**: gRPC (与EdgeAPI通信)
- **认证方式**: EdgeAPI AccessToken机制
- **数据格式**: JSON
- **文档工具**: Swagger/OpenAPI 3.0

## 2. 技术架构

### 2.1 整体架构
```
企业客户端 → EdgeOpenAPI (胶水层) → EdgeAPI (现有系统) → 数据库
```

### 2.2 核心组件
1. **HTTP服务层**: Gin框架提供RESTful API
2. **认证中间件**: 调用EdgeAPI认证服务
3. **路由转换层**: RESTful路径转换为gRPC调用
4. **格式转换层**: JSON与Protobuf格式转换
5. **gRPC客户端层**: 与EdgeAPI通信

### 2.3 功能复用策略

EdgeOpenAPI完全复用EdgeAPI现有功能：

| 功能模块 | 复用方式 | EdgeAPI服务 |
|---------|---------|-------------|
| 用户认证 | 调用认证服务 | APIAccessTokenService |
| 用户管理 | 调用用户服务 | UserService |
| 集群管理 | 调用集群服务 | NodeClusterService |
| 节点管理 | 调用节点服务 | NodeService |
| CDN服务 | 调用服务器服务 | ServerService |
| 统计分析 | 调用统计服务 | StatService |
| 计费管理 | 调用计费服务 | UserBillService |
| 权限控制 | EdgeAPI自动验证 | 所有服务内置权限检查 |

### 2.4 权限控制矩阵

| 功能模块 | Platform Admin | Enterprise Admin | End User |
|---------|---------------|------------------|----------|
| 用户管理 | ✅ 所有用户 | ✅ 下级用户 | ❌ |
| 集群管理 | ✅ 所有集群 | ✅ 自有集群 | ❌ |
| 节点管理 | ✅ 所有节点 | ✅ 购买使用权 | ❌ |
| CDN服务 | ✅ 所有服务 | ✅ 企业服务 | ✅ 个人服务 |
| 计费查看 | ✅ 平台账单 | ✅ 企业账单 | ❌ |
| 统计分析 | ✅ 全平台 | ✅ 企业维度 | ✅ 个人维度 |

## 3. API接口设计

### 3.1 接口规范
- **Base URL**: `https://api.example.com/v1`
- **认证方式**: `Authorization: Bearer <JWT_TOKEN>`
- **API Key**: `X-API-Key: <API_KEY>`
- **内容类型**: `Content-Type: application/json`

### 3.2 响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": 1640995200,
  "request_id": "req_123456789"
}
```

### 3.3 核心API模块

#### 3.3.1 用户管理API
```
GET    /api/v1/users                    # 获取下级用户列表
POST   /api/v1/users                    # 创建下级用户
GET    /api/v1/users/{id}               # 获取用户详情
PUT    /api/v1/users/{id}               # 更新用户信息
DELETE /api/v1/users/{id}               # 删除用户
PUT    /api/v1/users/{id}/status        # 启用/禁用用户
```

#### 3.3.2 集群管理API
```
GET    /api/v1/clusters                 # 获取企业集群列表
POST   /api/v1/clusters                 # 创建集群
GET    /api/v1/clusters/{id}            # 获取集群详情
PUT    /api/v1/clusters/{id}            # 更新集群配置
DELETE /api/v1/clusters/{id}            # 删除集群
```

#### 3.3.3 节点管理API
```
GET    /api/v1/nodes                    # 获取节点列表
GET    /api/v1/nodes/{id}               # 获取节点详情
POST   /api/v1/nodes/{id}/usage-rights  # 购买节点使用权
GET    /api/v1/usage-rights             # 获取使用权列表
```

#### 3.3.4 CDN服务API
```
GET    /api/v1/services                 # 获取CDN服务列表
POST   /api/v1/services                 # 创建CDN服务
GET    /api/v1/services/{id}            # 获取服务详情
PUT    /api/v1/services/{id}            # 更新服务配置
DELETE /api/v1/services/{id}            # 删除服务
POST   /api/v1/services/{id}/deploy     # 部署服务
```

#### 3.3.5 统计分析API
```
GET    /api/v1/stats/traffic            # 流量统计
GET    /api/v1/stats/bandwidth          # 带宽统计
GET    /api/v1/stats/requests           # 请求统计
GET    /api/v1/stats/errors             # 错误统计
GET    /api/v1/stats/performance        # 性能统计
```

#### 3.3.6 计费管理API
```
GET    /api/v1/billing/accounts         # 获取账户信息
GET    /api/v1/billing/bills            # 获取账单列表
GET    /api/v1/billing/bills/{id}       # 获取账单详情
GET    /api/v1/billing/usage            # 获取使用量统计
GET    /api/v1/billing/quotas           # 获取配额信息
```

## 4. 权限控制与安全机制

### 4.1 认证机制
EdgeOpenAPI完全复用EdgeAPI现有的认证体系：

1. **AccessKey认证**: 使用EdgeAPI的UserAccessKey表
2. **AccessToken机制**: 调用EdgeAPI的APIAccessTokenService
3. **企业用户验证**: 依赖EdgeAPI的用户权限体系

### 4.2 认证流程实现
```go
// 认证中间件（复用EdgeAPI）
func AuthMiddleware(edgeAPIClient *EdgeAPIClient) gin.HandlerFunc {
    return func(c *gin.Context) {
        // 1. 提取认证信息
        accessKeyId := c.GetHeader("X-API-Key-ID")
        accessKey := c.GetHeader("X-API-Key")

        if accessKeyId == "" || accessKey == "" {
            c.JSON(401, gin.H{"error": "Missing API credentials"})
            c.Abort()
            return
        }

        // 2. 调用EdgeAPI获取AccessToken
        ctx := context.Background()
        resp, err := edgeAPIClient.APIAccessTokenService.GetAPIAccessToken(ctx, &pb.GetAPIAccessTokenRequest{
            Type:        "user",
            AccessKeyId: accessKeyId,
            AccessKey:   accessKey,
        })

        if err != nil {
            c.JSON(401, gin.H{"error": "Invalid API credentials"})
            c.Abort()
            return
        }

        // 3. 设置上下文信息
        c.Set("access_token", resp.Token)
        c.Set("expires_at", resp.ExpiresAt)
        c.Next()
    }
}
```

### 4.3 权限控制策略
- **完全依赖EdgeAPI**: 所有权限验证由EdgeAPI服务自动处理
- **数据隔离**: 基于现有UserId租户隔离机制
- **权限范围**: 自动限制在Enterprise Admin权限范围内

## 5. EdgeAPI集成实现

### 5.1 gRPC客户端封装
```go
// EdgeAPI客户端封装
type EdgeAPIClient struct {
    conn                    *grpc.ClientConn
    APIAccessTokenService  pb.APIAccessTokenServiceClient
    UserService            pb.UserServiceClient
    ServerService          pb.ServerServiceClient
    NodeService            pb.NodeServiceClient
    NodeClusterService     pb.NodeClusterServiceClient
    StatService            pb.StatServiceClient
    UserBillService        pb.UserBillServiceClient
}

func NewEdgeAPIClient(endpoint string) (*EdgeAPIClient, error) {
    conn, err := grpc.Dial(endpoint, grpc.WithInsecure())
    if err != nil {
        return nil, err
    }

    return &EdgeAPIClient{
        conn:                   conn,
        APIAccessTokenService: pb.NewAPIAccessTokenServiceClient(conn),
        UserService:           pb.NewUserServiceClient(conn),
        ServerService:         pb.NewServerServiceClient(conn),
        NodeService:           pb.NewNodeServiceClient(conn),
        NodeClusterService:    pb.NewNodeClusterServiceClient(conn),
        StatService:           pb.NewStatServiceClient(conn),
        UserBillService:       pb.NewUserBillServiceClient(conn),
    }, nil
}
```

### 5.2 格式转换实现
```go
// 通用格式转换器
type Converter struct{}

func (c *Converter) UserFromProto(pb *pb.User) *User {
    return &User{
        ID:        pb.Id,
        Username:  pb.Username,
        Fullname:  pb.Fullname,
        Email:     pb.Email,
        Mobile:    pb.Mobile,
        Status:    pb.IsOn,
        CreatedAt: time.Unix(pb.CreatedAt, 0),
    }
}

func (c *Converter) CreateUserToProto(user *CreateUserRequest) *pb.CreateUserRequest {
    return &pb.CreateUserRequest{
        Username: user.Username,
        Password: user.Password,
        Fullname: user.Fullname,
        Email:    user.Email,
        Mobile:   user.Mobile,
    }
}
```

### 5.3 API处理器实现
```go
// 用户管理处理器（只做转换）
func ListUsers(client *EdgeAPIClient) gin.HandlerFunc {
    return func(c *gin.Context) {
        // 获取认证信息
        accessToken := c.GetString("access_token")
        ctx := metadata.NewOutgoingContext(context.Background(), metadata.Pairs(
            "authorization", "Bearer "+accessToken,
        ))

        // 调用EdgeAPI服务
        resp, err := client.UserService.ListEnabledUsers(ctx, &pb.ListEnabledUsersRequest{
            // EdgeAPI会自动根据AccessToken验证权限和数据隔离
        })

        if err != nil {
            c.JSON(500, gin.H{"error": err.Error()})
            return
        }

        // 转换响应格式
        users := make([]*User, len(resp.Users))
        converter := &Converter{}
        for i, user := range resp.Users {
            users[i] = converter.UserFromProto(user)
        }

        c.JSON(200, gin.H{
            "code": 200,
            "message": "success",
            "data": gin.H{"users": users},
        })
    }
}
```

## 6. 开发实施方案

### 6.1 开发阶段划分

**第1周：基础框架和核心转换**
- Day 1: 项目脚手架和EdgeAPI客户端封装
- Day 2: 认证中间件和格式转换器
- Day 3: 用户管理API转换
- Day 4: CDN服务管理API转换
- Day 5: 集群和节点管理API转换

**第2周：高级功能和文档**
- Day 1: 统计分析API转换
- Day 2: 计费管理API转换
- Day 3: 批量操作API封装
- Day 4: API文档生成和测试
- Day 5: 部署和上线

### 6.2 里程碑规划

**里程碑1：核心功能完成 (1周)**
- EdgeAPI客户端集成
- 基础API转换功能
- 认证机制实现

**里程碑2：功能完整版 (2周)**
- 所有API模块完成
- 批量操作功能
- API文档生成

### 6.3 技术风险控制

**主要风险及应对**：
1. **gRPC通信稳定性**: 实现连接池和重试机制
2. **格式转换错误**: 通过完整测试覆盖解决
3. **API设计不当**: 通过企业客户反馈迭代解决

## 7. 项目目录结构

```
EdgeOpenAPI/
├── cmd/
│   └── server/
│       └── main.go                 # 服务启动入口
├── internal/
│   ├── handlers/                   # HTTP处理器（只做转换）
│   ├── middleware/                 # 认证中间件（调用EdgeAPI）
│   ├── grpc/                       # gRPC客户端封装
│   ├── models/                     # 请求/响应模型（JSON格式）
│   └── routes/                     # 路由定义
├── pkg/
│   ├── converter/                  # 格式转换工具
│   └── client/                     # EdgeAPI客户端SDK
├── configs/                        # 配置文件
├── docs/                          # API文档
├── tests/                         # 测试文件
├── go.mod
├── go.sum
├── Dockerfile
└── README.md
```

## 8. 核心特性

### 8.1 EdgeOpenAPI的独特价值
1. **协议转换**: 将gRPC转换为企业友好的RESTful API
2. **零业务逻辑**: 完全依赖EdgeAPI，确保数据一致性
3. **完全兼容**: 与EdgeAPI现有功能100%兼容
4. **快速开发**: 2周即可完成全部功能开发
5. **低维护成本**: 只需维护协议转换层

### 8.2 技术优势
- **高性能**: Gin框架提供高性能HTTP服务
- **高可靠**: 完全复用EdgeAPI的成熟业务逻辑
- **易扩展**: 新增API只需添加转换层
- **易维护**: 业务逻辑变更无需修改EdgeOpenAPI

## 9. 总结

EdgeOpenAPI作为EdgeAPI的API网关，通过最小化的实现提供了企业级的RESTful API服务。其核心价值在于协议转换和企业级API封装，完全复用EdgeAPI现有的认证、权限、业务逻辑和数据模型，确保了系统的一致性和可靠性。

该方案具有开发周期短、技术风险低、维护成本低的特点，是一个务实高效的技术解决方案。
