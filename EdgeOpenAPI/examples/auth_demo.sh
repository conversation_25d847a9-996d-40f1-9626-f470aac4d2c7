#!/bin/bash

# EdgeOpenAPI 认证功能演示脚本
# 此脚本演示如何使用 EdgeOpenAPI 的认证端点

set -e

# 配置
BASE_URL="http://localhost:8080"
USERNAME="customer_1"
PASSWORD="customer_1"
API_KEY_ID="your_api_key_id"
API_KEY="your_api_key"

echo "=== EdgeOpenAPI 认证功能演示 ==="
echo

# 1. 健康检查
echo "1. 检查服务健康状态..."
curl -s -X GET "$BASE_URL/health" | jq '.'
echo

# 2. 用户登录演示
echo "2. 用户登录演示..."
echo "请求: POST $BASE_URL/api/v1/auth/login"
echo "请求体: {\"username\": \"$USERNAME\", \"password\": \"$PASSWORD\"}"

LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d "{
    \"username\": \"$USERNAME\",
    \"password\": \"$PASSWORD\"
  }")

echo "响应:"
echo "$LOGIN_RESPONSE" | jq '.'
echo

# 3. 登录失败演示
echo "3. 登录失败演示（错误密码）..."
echo "请求: POST $BASE_URL/api/v1/auth/login"
echo "请求体: {\"username\": \"$USERNAME\", \"password\": \"wrong_password\"}"

FAILED_LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d "{
    \"username\": \"$USERNAME\",
    \"password\": \"wrong_password\"
  }")

echo "响应:"
echo "$FAILED_LOGIN_RESPONSE" | jq '.'
echo

# 4. 输入验证演示
echo "4. 输入验证演示（缺少必填字段）..."
echo "请求: POST $BASE_URL/api/v1/auth/login"
echo "请求体: {\"username\": \"$USERNAME\"}"

VALIDATION_ERROR_RESPONSE=$(curl -s -X POST "$BASE_URL/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d "{
    \"username\": \"$USERNAME\"
  }")

echo "响应:"
echo "$VALIDATION_ERROR_RESPONSE" | jq '.'
echo

# 5. 令牌验证演示（需要有效的 API 密钥）
echo "5. 令牌验证演示..."
echo "请求: POST $BASE_URL/api/v1/auth/validate"
echo "请求头: X-API-Key-ID: $API_KEY_ID, X-API-Key: $API_KEY"

if [ "$API_KEY_ID" != "your_api_key_id" ] && [ "$API_KEY" != "your_api_key" ]; then
    VALIDATE_RESPONSE=$(curl -s -X POST "$BASE_URL/api/v1/auth/validate" \
      -H "X-API-Key-ID: $API_KEY_ID" \
      -H "X-API-Key: $API_KEY")
    
    echo "响应:"
    echo "$VALIDATE_RESPONSE" | jq '.'
else
    echo "跳过令牌验证演示 - 请设置有效的 API_KEY_ID 和 API_KEY"
fi
echo

# 6. 无认证访问演示
echo "6. 无认证访问演示（应该失败）..."
echo "请求: POST $BASE_URL/api/v1/auth/validate"
echo "请求头: 无认证头"

NO_AUTH_RESPONSE=$(curl -s -X POST "$BASE_URL/api/v1/auth/validate")

echo "响应:"
echo "$NO_AUTH_RESPONSE" | jq '.'
echo

# 7. 访问密钥管理演示（未实现功能）
echo "7. 访问密钥管理演示（未实现功能）..."
echo "请求: POST $BASE_URL/api/v1/auth/access-keys"

if [ "$API_KEY_ID" != "your_api_key_id" ] && [ "$API_KEY" != "your_api_key" ]; then
    ACCESS_KEY_RESPONSE=$(curl -s -X POST "$BASE_URL/api/v1/auth/access-keys" \
      -H "X-API-Key-ID: $API_KEY_ID" \
      -H "X-API-Key: $API_KEY" \
      -H "Content-Type: application/json" \
      -d "{
        \"description\": \"Demo access key\"
      }")
    
    echo "响应:"
    echo "$ACCESS_KEY_RESPONSE" | jq '.'
else
    echo "跳过访问密钥管理演示 - 请设置有效的 API_KEY_ID 和 API_KEY"
fi
echo

echo "=== 演示完成 ==="
echo
echo "注意事项:"
echo "1. 确保 EdgeOpenAPI 服务正在运行在 $BASE_URL"
echo "2. 确保 EdgeAPI 服务正在运行并可访问"
echo "3. 使用真实的用户名/密码和 API 密钥进行测试"
echo "4. 某些功能（如访问密钥管理）尚未完全实现"
