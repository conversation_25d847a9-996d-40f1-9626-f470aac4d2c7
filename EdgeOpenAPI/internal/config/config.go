package config

import (
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"time"

	"github.com/iwind/TeaGo/Tea"
	"gopkg.in/yaml.v3"
)

// Config represents the application configuration
type Config struct {
	Server struct {
		Host string `yaml:"host"`
		Port int    `yaml:"port"`
		Mode string `yaml:"mode"`
	} `yaml:"server"`

	EdgeAPI struct {
		Endpoint  string `yaml:"endpoint"`
		Timeout   string `yaml:"timeout"`
		Keepalive struct {
			Time                string `yaml:"time"`
			Timeout             string `yaml:"timeout"`
			PermitWithoutStream bool   `yaml:"permit_without_stream"`
		} `yaml:"keepalive"`
	} `yaml:"edgeapi"`

	APINode struct {
		NodeID string `yaml:"node_id"`
		Secret string `yaml:"secret"`
	} `yaml:"api_node"`

	Logging struct {
		Level  string `yaml:"level"`
		Format string `yaml:"format"`
		Output string `yaml:"output"`
	} `yaml:"logging"`

	CORS struct {
		AllowedOrigins   []string `yaml:"allowed_origins"`
		AllowedMethods   []string `yaml:"allowed_methods"`
		AllowedHeaders   []string `yaml:"allowed_headers"`
		ExposedHeaders   []string `yaml:"expose_headers"`
		AllowCredentials bool     `yaml:"allow_credentials"`
	} `yaml:"cors"`

	RateLimit struct {
		Enabled           bool `yaml:"enabled"`
		RequestsPerMinute int  `yaml:"requests_per_minute"`
		Burst             int  `yaml:"burst"`
	} `yaml:"rate_limit"`

	Security struct {
		RequestTimeout string `yaml:"request_timeout"`
		MaxRequestSize string `yaml:"max_request_size"`
		Secret         string `yaml:"secret"` // 用于CSRF token生成的密钥
	} `yaml:"security"`
}

var globalConfig *Config

// LoadConfig loads configuration from the specified file or default locations
func LoadConfig(configPath ...string) (*Config, error) {
	var paths []string
	
	// 如果指定了配置文件路径，优先使用
	if len(configPath) > 0 && configPath[0] != "" {
		paths = append(paths, configPath[0])
	}
	
	// 默认配置文件路径
	paths = append(paths, 
		"configs/config.yaml",
		"config.yaml",
		Tea.ConfigFile("config.yaml"),
	)
	
	// 尝试从用户主目录读取
	if homeDir, err := os.UserHomeDir(); err == nil {
		paths = append(paths, filepath.Join(homeDir, ".edgeopenapi", "config.yaml"))
	}
	
	// 尝试从系统配置目录读取
	paths = append(paths, "/etc/edgeopenapi/config.yaml")

	var data []byte
	var err error
	var usedPath string
	
	for _, path := range paths {
		data, err = os.ReadFile(path)
		if err == nil {
			usedPath = path
			break
		}
	}
	
	if err != nil {
		return nil, fmt.Errorf("failed to read config file from any of the paths %v: %w", paths, err)
	}

	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to parse config file %s: %w", usedPath, err)
	}

	// 验证必要的配置
	if err := config.validate(); err != nil {
		return nil, fmt.Errorf("config validation failed: %w", err)
	}

	// 设置默认值
	config.setDefaults()
	
	// 设置全局配置
	globalConfig = &config

	return &config, nil
}

// GetConfig returns the global configuration instance
func GetConfig() *Config {
	if globalConfig == nil {
		panic("config not loaded, call LoadConfig first")
	}
	return globalConfig
}

// validate validates the configuration
func (c *Config) validate() error {
	if c.APINode.NodeID == "" {
		return errors.New("api_node.node_id is required")
	}
	if c.APINode.Secret == "" {
		return errors.New("api_node.secret is required")
	}
	return nil
}

// setDefaults sets default values for configuration
func (c *Config) setDefaults() {
	// Server defaults
	if c.Server.Host == "" {
		c.Server.Host = "0.0.0.0"
	}
	if c.Server.Port == 0 {
		c.Server.Port = 8080
	}
	if c.Server.Mode == "" {
		c.Server.Mode = "release"
	}

	// EdgeAPI defaults
	if c.EdgeAPI.Endpoint == "" {
		c.EdgeAPI.Endpoint = "localhost:8001"
	}
	if c.EdgeAPI.Timeout == "" {
		c.EdgeAPI.Timeout = "30s"
	}
	if c.EdgeAPI.Keepalive.Time == "" {
		c.EdgeAPI.Keepalive.Time = "10s"
	}
	if c.EdgeAPI.Keepalive.Timeout == "" {
		c.EdgeAPI.Keepalive.Timeout = "1s"
	}

	// Logging defaults
	if c.Logging.Level == "" {
		c.Logging.Level = "info"
	}
	if c.Logging.Format == "" {
		c.Logging.Format = "json"
	}
	if c.Logging.Output == "" {
		c.Logging.Output = "stdout"
	}

	// Security defaults
	if c.Security.RequestTimeout == "" {
		c.Security.RequestTimeout = "30s"
	}
	if c.Security.MaxRequestSize == "" {
		c.Security.MaxRequestSize = "10MB"
	}
	if c.Security.Secret == "" {
		// 使用API节点密钥作为默认的安全密钥
		c.Security.Secret = c.APINode.Secret
	}

	// CORS defaults
	if len(c.CORS.AllowedOrigins) == 0 {
		c.CORS.AllowedOrigins = []string{"*"}
	}
	if len(c.CORS.AllowedMethods) == 0 {
		c.CORS.AllowedMethods = []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}
	}
	if len(c.CORS.AllowedHeaders) == 0 {
		c.CORS.AllowedHeaders = []string{"Origin", "Content-Type", "Accept", "Authorization", "X-API-Key-ID", "X-API-Key"}
	}
}

// GetServerTimeout returns the server timeout as time.Duration
func (c *Config) GetServerTimeout() time.Duration {
	if duration, err := time.ParseDuration(c.Security.RequestTimeout); err == nil {
		return duration
	}
	return 30 * time.Second // default
}

// GetEdgeAPITimeout returns the EdgeAPI timeout as time.Duration
func (c *Config) GetEdgeAPITimeout() time.Duration {
	if duration, err := time.ParseDuration(c.EdgeAPI.Timeout); err == nil {
		return duration
	}
	return 30 * time.Second // default
}

// GetKeepaliveTime returns the keepalive time as time.Duration
func (c *Config) GetKeepaliveTime() time.Duration {
	if duration, err := time.ParseDuration(c.EdgeAPI.Keepalive.Time); err == nil {
		return duration
	}
	return 10 * time.Second // default
}

// GetKeepaliveTimeout returns the keepalive timeout as time.Duration
func (c *Config) GetKeepaliveTimeout() time.Duration {
	if duration, err := time.ParseDuration(c.EdgeAPI.Keepalive.Timeout); err == nil {
		return duration
	}
	return 1 * time.Second // default
}
