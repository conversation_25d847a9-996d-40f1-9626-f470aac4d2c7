package csrf

import (
	"sync"
	"time"
)

var sharedTokenManager = NewTokenManager()

func init() {
	go func() {
		ticker := time.NewTicker(1 * time.Hour)
		for range ticker.C {
			sharedTokenManager.Clean()
		}
	}()
}

// TokenManager 管理CSRF token的生命周期
type TokenManager struct {
	tokenMap map[string]int64 // token => timestamp

	locker sync.Mutex
}

// NewTokenManager 创建新的token管理器
func NewTokenManager() *TokenManager {
	return &TokenManager{
		tokenMap: map[string]int64{},
	}
}

// Put 存储token
func (this *TokenManager) Put(token string) {
	this.locker.Lock()
	this.tokenMap[token] = time.Now().Unix()
	this.locker.Unlock()
}

// Exists 检查token是否存在
func (this *TokenManager) Exists(token string) bool {
	this.locker.Lock()
	_, ok := this.tokenMap[token]
	this.locker.Unlock()
	return ok
}

// Delete 删除token
func (this *TokenManager) Delete(token string) {
	this.locker.Lock()
	delete(this.tokenMap, token)
	this.locker.Unlock()
}

// Clean 清理过期的token
func (this *TokenManager) Clean() {
	this.locker.Lock()
	for token, timestamp := range this.tokenMap {
		if time.Now().Unix()-timestamp > 3600 { // 删除一个小时前的
			delete(this.tokenMap, token)
		}
	}
	this.locker.Unlock()
}

// Count 返回当前token数量
func (this *TokenManager) Count() int {
	this.locker.Lock()
	count := len(this.tokenMap)
	this.locker.Unlock()
	return count
}
