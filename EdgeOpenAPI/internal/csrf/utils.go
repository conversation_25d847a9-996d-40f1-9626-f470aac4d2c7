package csrf

import (
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	"strconv"
	"time"

	"github.com/TeaOSLab/EdgeOpenAPI/internal/config"
	"github.com/iwind/TeaGo/types"
)

// Generate 生成CSRF Token
func Generate() string {
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)

	// 获取配置中的密钥
	cfg := config.GetConfig()
	secret := cfg.Security.Secret

	h := sha256.New()
	h.Write([]byte(secret))
	h.Write([]byte(timestamp))
	s := h.Sum(nil)
	token := base64.StdEncoding.EncodeToString([]byte(timestamp + fmt.Sprintf("%x", s)))
	sharedTokenManager.Put(token)
	return token
}

// Validate 校验CSRF Token
func Validate(token string) (b bool) {
	if len(token) == 0 {
		return
	}

	if !sharedTokenManager.Exists(token) {
		return
	}
	defer func() {
		sharedTokenManager.Delete(token)
	}()

	data, err := base64.StdEncoding.DecodeString(token)
	if err != nil {
		return
	}

	hashString := string(data)
	if len(hashString) < 10+64 { // 10位时间戳 + 64位SHA256哈希
		return
	}

	timestampString := hashString[:10]
	hashString = hashString[10:]

	// 获取配置中的密钥
	cfg := config.GetConfig()
	secret := cfg.Security.Secret

	h := sha256.New()
	h.Write([]byte(secret))
	h.Write([]byte(timestampString))
	hashData := h.Sum(nil)
	if hashString != fmt.Sprintf("%x", hashData) {
		return
	}

	timestamp := types.Int64(timestampString)
	if timestamp < time.Now().Unix()-1800 { // 有效期半个小时
		return
	}

	return true
}

// GetTokenManager 获取token管理器实例（用于测试）
func GetTokenManager() *TokenManager {
	return sharedTokenManager
}
