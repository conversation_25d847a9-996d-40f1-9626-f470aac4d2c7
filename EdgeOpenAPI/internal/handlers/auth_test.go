package handlers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/TeaOSLab/EdgeOpenAPI/pkg/converter"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestAuthHandler_Login_ValidationError(t *testing.T) {
	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	// Create a test router
	router := gin.New()
	
	// Create a mock auth handler (we'll need to mock the EdgeAPI client later)
	authHandler := &AuthHandler{
		edgeAPIClient: nil, // For this test, we're only testing validation
	}

	// Register the route
	router.POST("/api/v1/auth/login", authHandler.Login)

	// Test case: missing required fields
	t.Run("missing username", func(t *testing.T) {
		reqBody := converter.LoginRequest{
			Password: "password123",
		}
		
		jsonBody, _ := json.Marshal(reqBody)
		req, _ := http.NewRequest("POST", "/api/v1/auth/login", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
		
		assert.Equal(t, http.StatusBadRequest, w.Code)
		
		var response converter.APIResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, http.StatusBadRequest, response.Code)
		assert.Contains(t, response.Message, "Invalid request parameters")
	})

	// Test case: missing password
	t.Run("missing password", func(t *testing.T) {
		reqBody := converter.LoginRequest{
			Username: "testuser",
		}
		
		jsonBody, _ := json.Marshal(reqBody)
		req, _ := http.NewRequest("POST", "/api/v1/auth/login", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
		
		assert.Equal(t, http.StatusBadRequest, w.Code)
		
		var response converter.APIResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, http.StatusBadRequest, response.Code)
		assert.Contains(t, response.Message, "Invalid request parameters")
	})

	// Test case: empty username and password
	t.Run("empty credentials", func(t *testing.T) {
		reqBody := converter.LoginRequest{
			Username: "",
			Password: "",
		}
		
		jsonBody, _ := json.Marshal(reqBody)
		req, _ := http.NewRequest("POST", "/api/v1/auth/login", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
		
		assert.Equal(t, http.StatusBadRequest, w.Code)
		
		var response converter.APIResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, http.StatusBadRequest, response.Code)
		assert.Contains(t, response.Message, "Invalid request parameters")
	})
}

func TestAuthHandler_ValidateToken(t *testing.T) {
	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	// Create a test router
	router := gin.New()
	
	// Create a mock auth handler
	authHandler := &AuthHandler{
		edgeAPIClient: nil,
	}

	// Register the route
	router.POST("/api/v1/auth/validate", authHandler.ValidateToken)

	// Test case: no token in context (should fail)
	t.Run("no token", func(t *testing.T) {
		req, _ := http.NewRequest("POST", "/api/v1/auth/validate", nil)
		
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
		
		assert.Equal(t, http.StatusUnauthorized, w.Code)
		
		var response converter.APIResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, http.StatusUnauthorized, response.Code)
		assert.Contains(t, response.Message, "Authentication failed")
	})

	// Test case: with valid token in context
	t.Run("with token", func(t *testing.T) {
		// Create a router with middleware that sets a token
		routerWithToken := gin.New()
		routerWithToken.Use(func(c *gin.Context) {
			c.Set("access_token", "test_token")
			c.Next()
		})
		routerWithToken.POST("/api/v1/auth/validate", authHandler.ValidateToken)
		
		req, _ := http.NewRequest("POST", "/api/v1/auth/validate", nil)
		
		w := httptest.NewRecorder()
		routerWithToken.ServeHTTP(w, req)
		
		assert.Equal(t, http.StatusOK, w.Code)
		
		var response converter.APIResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, http.StatusOK, response.Code)
		assert.Equal(t, "success", response.Message)
	})
}

func TestAuthHandler_CreateAccessKey_NotImplemented(t *testing.T) {
	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	// Create a test router
	router := gin.New()
	
	// Create a mock auth handler
	authHandler := &AuthHandler{
		edgeAPIClient: nil,
	}

	// Add middleware to simulate authentication
	router.Use(func(c *gin.Context) {
		c.Set("access_token", "test_token")
		c.Next()
	})

	// Register the route
	router.POST("/api/v1/auth/access-keys", authHandler.CreateAccessKey)

	// Test case: should return not implemented
	t.Run("not implemented", func(t *testing.T) {
		reqBody := converter.CreateAccessKeyRequest{
			Description: "Test access key",
		}
		
		jsonBody, _ := json.Marshal(reqBody)
		req, _ := http.NewRequest("POST", "/api/v1/auth/access-keys", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
		
		assert.Equal(t, http.StatusNotImplemented, w.Code)
		
		var response converter.APIResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, http.StatusNotImplemented, response.Code)
		assert.Contains(t, response.Message, "not yet implemented")
	})
}
