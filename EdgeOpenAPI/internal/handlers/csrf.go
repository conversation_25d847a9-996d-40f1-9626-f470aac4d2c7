package handlers

import (
	"net/http"
	"sync"
	"time"

	"github.com/TeaOSLab/EdgeOpenAPI/internal/csrf"
	"github.com/TeaOSLab/EdgeOpenAPI/pkg/converter"
	"github.com/gin-gonic/gin"
)

var (
	lastTimestamp = int64(0)
	csrfLocker    sync.Mutex
)

// CSRFHandler handles CSRF token related requests
type CSRFHandler struct{}

// NewCSRFHandler creates a new CSRF handler
func NewCSRFHandler() *CSRFHandler {
	return &CSRFHandler{}
}

// GetToken handles GET /csrf/token
// 获取CSRF token，用于后续的登录请求
func (h *CSRFHandler) GetToken(c *gin.Context) {
	csrfLocker.Lock()
	defer csrfLocker.Unlock()

	defer func() {
		lastTimestamp = time.Now().Unix()
	}()

	// 限制请求速度，防止滥用
	// 如果没有认证用户且请求过于频繁，则拒绝请求
	currentTime := time.Now().Unix()
	if lastTimestamp > 0 && currentTime-lastTimestamp <= 0 {
		converter.ErrorResponse(c, http.StatusTooManyRequests, "请求速度过快，请稍后重试", gin.H{
			"type":    "rate_limit_error",
			"details": "CSRF token请求过于频繁",
		})
		return
	}

	// 生成新的CSRF token
	token := csrf.Generate()

	converter.SuccessResponse(c, gin.H{
		"token": token,
	})
}

// ValidateToken 验证CSRF token（中间件使用）
func ValidateToken(token string) bool {
	return csrf.Validate(token)
}
