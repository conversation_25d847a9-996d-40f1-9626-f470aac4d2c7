package handlers

import (
	"context"
	"strconv"

	"github.com/TeaOSLab/EdgeCommon/pkg/rpc/pb"
	"github.com/TeaOSLab/EdgeOpenAPI/internal/grpc"
	"github.com/TeaOSLab/EdgeOpenAPI/internal/middleware"
	"github.com/TeaOSLab/EdgeOpenAPI/pkg/converter"
	"github.com/gin-gonic/gin"
	"google.golang.org/grpc/metadata"
)

// UserHandler handles user-related HTTP requests
type UserHandler struct {
	edgeAPIClient *grpc.EdgeAPIClient
}

// NewUserHandler creates a new user handler
func NewUserHandler(edgeAPIClient *grpc.EdgeAPIClient) *UserHandler {
	return &UserHandler{
		edgeAPIClient: edgeAPIClient,
	}
}

// ListUsers handles GET /api/v1/users
func (h *UserHandler) ListUsers(c *gin.Context) {
	// Parse query parameters
	page, _ := strconv.ParseInt(c.<PERSON>faultQuery("page", "1"), 10, 64)
	size, _ := strconv.ParseInt(c.DefaultQuery("size", "20"), 10, 64)
	keyword := c.Query("keyword")

	// Create authenticated context
	accessToken := middleware.GetAccessTokenFromContext(c)
	ctx := metadata.NewOutgoingContext(context.Background(), metadata.Pairs(
		"authorization", "Bearer "+accessToken,
	))

	// Calculate offset
	offset := (page - 1) * size

	// Call EdgeAPI to get users
	resp, err := h.edgeAPIClient.UserService.ListEnabledUsers(ctx, &pb.ListEnabledUsersRequest{
		Offset:  offset,
		Size:    size,
		Keyword: keyword,
	})
	if err != nil {
		converter.InternalErrorResponse(c, err)
		return
	}

	// Convert protobuf users to JSON
	users := converter.UsersFromProto(resp.Users)

	// Get total count for pagination
	countResp, err := h.edgeAPIClient.UserService.CountAllEnabledUsers(ctx, &pb.CountAllEnabledUsersRequest{
		Keyword: keyword,
	})
	if err != nil {
		converter.InternalErrorResponse(c, err)
		return
	}

	// Create pagination info
	pagination := &converter.PaginationInfo{
		Page:  page,
		Size:  size,
		Total: countResp.Count,
		Pages: converter.CalculatePages(countResp.Count, size),
	}

	converter.SuccessResponseWithPagination(c, users, pagination)
}

// GetUser handles GET /api/v1/users/{id}
func (h *UserHandler) GetUser(c *gin.Context) {
	// Parse user ID from URL
	userIdStr := c.Param("id")
	userId, err := strconv.ParseInt(userIdStr, 10, 64)
	if err != nil {
		converter.ValidationErrorResponse(c, gin.H{
			"field":   "id",
			"message": "Invalid user ID",
		})
		return
	}

	// Create authenticated context
	accessToken := middleware.GetAccessTokenFromContext(c)
	ctx := metadata.NewOutgoingContext(context.Background(), metadata.Pairs(
		"authorization", "Bearer "+accessToken,
	))

	// Call EdgeAPI to get user
	resp, err := h.edgeAPIClient.UserService.FindEnabledUser(ctx, &pb.FindEnabledUserRequest{
		UserId: userId,
	})
	if err != nil {
		converter.InternalErrorResponse(c, err)
		return
	}

	if resp.User == nil {
		converter.NotFoundResponse(c, "user")
		return
	}

	// Convert protobuf user to JSON
	user := converter.UserFromProto(resp.User)
	converter.SuccessResponse(c, user)
}

// CreateUser handles POST /api/v1/users
func (h *UserHandler) CreateUser(c *gin.Context) {
	var req converter.CreateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		converter.ValidationErrorResponse(c, err.Error())
		return
	}

	// Create authenticated context
	accessToken := middleware.GetAccessTokenFromContext(c)
	ctx := metadata.NewOutgoingContext(context.Background(), metadata.Pairs(
		"authorization", "Bearer "+accessToken,
	))

	// Convert to protobuf request
	pbReq := converter.CreateUserToProto(&req)

	// Call EdgeAPI to create user
	resp, err := h.edgeAPIClient.UserService.CreateUser(ctx, pbReq)
	if err != nil {
		converter.InternalErrorResponse(c, err)
		return
	}

	converter.SuccessResponse(c, gin.H{
		"user_id": resp.UserId,
		"message": "User created successfully",
	})
}

// UpdateUser handles PUT /api/v1/users/{id}
func (h *UserHandler) UpdateUser(c *gin.Context) {
	// Parse user ID from URL
	userIdStr := c.Param("id")
	userId, err := strconv.ParseInt(userIdStr, 10, 64)
	if err != nil {
		converter.ValidationErrorResponse(c, gin.H{
			"field":   "id",
			"message": "Invalid user ID",
		})
		return
	}

	var req converter.UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		converter.ValidationErrorResponse(c, err.Error())
		return
	}

	// Create authenticated context
	accessToken := middleware.GetAccessTokenFromContext(c)
	ctx := metadata.NewOutgoingContext(context.Background(), metadata.Pairs(
		"authorization", "Bearer "+accessToken,
	))

	// Convert to protobuf request
	pbReq := converter.UpdateUserToProto(userId, &req)

	// Call EdgeAPI to update user
	_, err = h.edgeAPIClient.UserService.UpdateUser(ctx, pbReq)
	if err != nil {
		converter.InternalErrorResponse(c, err)
		return
	}

	converter.SuccessResponse(c, gin.H{
		"message": "User updated successfully",
	})
}

// DeleteUser handles DELETE /api/v1/users/{id}
func (h *UserHandler) DeleteUser(c *gin.Context) {
	// Parse user ID from URL
	userIdStr := c.Param("id")
	userId, err := strconv.ParseInt(userIdStr, 10, 64)
	if err != nil {
		converter.ValidationErrorResponse(c, gin.H{
			"field":   "id",
			"message": "Invalid user ID",
		})
		return
	}

	// Create authenticated context
	accessToken := middleware.GetAccessTokenFromContext(c)
	ctx := metadata.NewOutgoingContext(context.Background(), metadata.Pairs(
		"authorization", "Bearer "+accessToken,
	))

	// Call EdgeAPI to delete user
	_, err = h.edgeAPIClient.UserService.DeleteUser(ctx, &pb.DeleteUserRequest{
		UserId: userId,
	})
	if err != nil {
		converter.InternalErrorResponse(c, err)
		return
	}

	converter.SuccessResponse(c, gin.H{
		"message": "User deleted successfully",
	})
}
