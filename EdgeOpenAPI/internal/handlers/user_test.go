package handlers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/TeaOSLab/EdgeOpenAPI/pkg/converter"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestUserHandler_CreateUser_ValidationError(t *testing.T) {
	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	// Create a test router
	router := gin.New()
	
	// Create a mock user handler (we'll need to mock the EdgeAPI client later)
	userHandler := &UserHandler{
		edgeAPIClient: nil, // For this test, we're only testing validation
	}

	// Register the route
	router.POST("/api/v1/users", userHandler.CreateUser)

	// Test case: missing required fields
	t.Run("missing required username", func(t *testing.T) {
		reqBody := converter.CreateUserRequest{
			Password: "password123",
			Fullname: "Test User",
		}
		
		jsonBody, _ := json.Marshal(reqBody)
		req, _ := http.NewRequest("POST", "/api/v1/users", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
		
		assert.Equal(t, http.StatusBadRequest, w.Code)
		
		var response converter.APIResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, http.StatusBadRequest, response.Code)
		assert.Contains(t, response.Message, "Invalid request parameters")
	})

	// Test case: missing required password
	t.Run("missing required password", func(t *testing.T) {
		reqBody := converter.CreateUserRequest{
			Username: "testuser",
			Fullname: "Test User",
		}
		
		jsonBody, _ := json.Marshal(reqBody)
		req, _ := http.NewRequest("POST", "/api/v1/users", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
		
		assert.Equal(t, http.StatusBadRequest, w.Code)
		
		var response converter.APIResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, http.StatusBadRequest, response.Code)
		assert.Contains(t, response.Message, "Invalid request parameters")
	})
}

func TestUserHandler_GetUser_InvalidID(t *testing.T) {
	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	// Create a test router
	router := gin.New()
	
	// Create a mock user handler
	userHandler := &UserHandler{
		edgeAPIClient: nil,
	}

	// Register the route
	router.GET("/api/v1/users/:id", userHandler.GetUser)

	// Test case: invalid user ID
	t.Run("invalid user ID", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/api/v1/users/invalid", nil)
		
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
		
		assert.Equal(t, http.StatusBadRequest, w.Code)
		
		var response converter.APIResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, http.StatusBadRequest, response.Code)
		assert.Contains(t, response.Message, "Invalid request parameters")
	})
}
