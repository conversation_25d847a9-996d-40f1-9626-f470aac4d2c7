package middleware

import (
	"context"
	"net/http"

	"github.com/TeaOSLab/EdgeOpenAPI/internal/grpc"
	"github.com/gin-gonic/gin"
)

// AuthMiddleware validates API credentials and sets access token in context
func AuthMiddleware(edgeAPIClient *grpc.EdgeAPIClient) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Skip authentication for health check endpoints and login
		if c.Request.URL.Path == "/health" ||
			c.Request.URL.Path == "/ping" ||
			c.Request.URL.Path == "/api/v1/auth/login" {
			c.Next()
			return
		}

		// Extract API credentials from headers
		accessKeyId := c.GetHeader("X-API-Key-ID")
		accessKey := c.GetHeader("X-API-Key")

		if accessKeyId == "" || accessKey == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    http.StatusUnauthorized,
				"message": "Missing API credentials",
				"error": gin.H{
					"type":    "authentication_error",
					"details": "X-API-Key-ID and X-API-Key headers are required",
				},
			})
			c.Abort()
			return
		}

		// Call EdgeAPI to validate credentials and get access token
		ctx := context.Background()
		resp, err := edgeAPIClient.GetAPIAccessToken(ctx, accessKeyId, accessKey)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    http.StatusUnauthorized,
				"message": "Invalid API credentials",
				"error": gin.H{
					"type":    "authentication_error",
					"details": err.Error(),
				},
			})
			c.Abort()
			return
		}

		// Set access token and expiration in context for use by handlers
		c.Set("access_token", resp.Token)
		c.Set("expires_at", resp.ExpiresAt)
		c.Next()
	}
}

// CORS middleware for handling cross-origin requests
func CORS() gin.HandlerFunc {
	return func(c *gin.Context) {
		origin := c.Request.Header.Get("Origin")

		// Allow specific origins or all origins for development
		if origin != "" {
			c.Header("Access-Control-Allow-Origin", origin)
		}

		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, X-API-Key-ID, X-API-Key")
		c.Header("Access-Control-Expose-Headers", "Content-Length")
		c.Header("Access-Control-Allow-Credentials", "true")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// GetAccessTokenFromContext retrieves the access token from gin context
func GetAccessTokenFromContext(c *gin.Context) string {
	if token, exists := c.Get("access_token"); exists {
		if tokenStr, ok := token.(string); ok {
			return tokenStr
		}
	}
	return ""
}

// CreateAuthenticatedContext creates a context with authorization metadata for gRPC calls
func CreateAuthenticatedContext(c *gin.Context) context.Context {
	accessToken := GetAccessTokenFromContext(c)
	if accessToken == "" {
		return context.Background()
	}

	// Add authorization header for gRPC metadata
	ctx := context.Background()
	// Note: We'll need to import google.golang.org/grpc/metadata for this
	// For now, we'll return the basic context and handle metadata in handlers
	return ctx
}
