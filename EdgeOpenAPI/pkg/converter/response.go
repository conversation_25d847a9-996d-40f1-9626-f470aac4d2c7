package converter

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// APIResponse represents the standard API response format
type APIResponse struct {
	Code      int         `json:"code"`
	Message   string      `json:"message"`
	Data      interface{} `json:"data,omitempty"`
	Error     interface{} `json:"error,omitempty"`
	Timestamp int64       `json:"timestamp"`
	RequestID string      `json:"request_id"`
}

// PaginationInfo represents pagination metadata
type PaginationInfo struct {
	Page  int64 `json:"page"`
	Size  int64 `json:"size"`
	Total int64 `json:"total"`
	Pages int64 `json:"pages"`
}

// SuccessResponse sends a successful API response
func SuccessResponse(c *gin.Context, data interface{}) {
	response := APIResponse{
		Code:      http.StatusOK,
		Message:   "success",
		Data:      data,
		Timestamp: time.Now().Unix(),
		RequestID: generateRequestID(),
	}
	c.<PERSON><PERSON>(http.StatusOK, response)
}

// SuccessResponseWithPagination sends a successful API response with pagination
func SuccessResponseWithPagination(c *gin.Context, data interface{}, pagination *PaginationInfo) {
	responseData := gin.H{
		"items":      data,
		"pagination": pagination,
	}
	
	response := APIResponse{
		Code:      http.StatusOK,
		Message:   "success",
		Data:      responseData,
		Timestamp: time.Now().Unix(),
		RequestID: generateRequestID(),
	}
	c.JSON(http.StatusOK, response)
}

// ErrorResponse sends an error API response
func ErrorResponse(c *gin.Context, code int, message string, err interface{}) {
	response := APIResponse{
		Code:      code,
		Message:   message,
		Error:     err,
		Timestamp: time.Now().Unix(),
		RequestID: generateRequestID(),
	}
	c.JSON(code, response)
}

// ValidationErrorResponse sends a validation error response
func ValidationErrorResponse(c *gin.Context, details interface{}) {
	errorInfo := gin.H{
		"type":    "validation_error",
		"details": details,
	}
	
	ErrorResponse(c, http.StatusBadRequest, "Invalid request parameters", errorInfo)
}

// InternalErrorResponse sends an internal server error response
func InternalErrorResponse(c *gin.Context, err error) {
	errorInfo := gin.H{
		"type":    "internal_error",
		"details": err.Error(),
	}
	
	ErrorResponse(c, http.StatusInternalServerError, "Internal server error", errorInfo)
}

// NotFoundResponse sends a not found error response
func NotFoundResponse(c *gin.Context, resource string) {
	errorInfo := gin.H{
		"type":     "not_found_error",
		"resource": resource,
	}
	
	ErrorResponse(c, http.StatusNotFound, "Resource not found", errorInfo)
}

// UnauthorizedResponse sends an unauthorized error response
func UnauthorizedResponse(c *gin.Context, message string) {
	errorInfo := gin.H{
		"type":    "authentication_error",
		"details": message,
	}
	
	ErrorResponse(c, http.StatusUnauthorized, "Authentication failed", errorInfo)
}

// generateRequestID generates a unique request ID
func generateRequestID() string {
	return "req_" + uuid.New().String()[:8]
}

// CalculatePages calculates the number of pages based on total and page size
func CalculatePages(total, size int64) int64 {
	if size <= 0 {
		return 0
	}
	return (total + size - 1) / size
}
