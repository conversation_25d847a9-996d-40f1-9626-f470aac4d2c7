# EdgeAPI B2B2C多租户CDN平台技术架构分析报告

## 1. 项目概述

### 1.1 项目定位和业务模式

EdgeAPI是一个基于Go语言开发的B2B2C多租户CDN平台，采用分布式架构设计，为企业客户提供完整的CDN服务能力，使企业客户能够为其终端用户提供CDN加速服务。

**业务模式特点：**
- **B2B2C三层架构**：平台管理员 → 企业客户 → 终端用户
- **多租户隔离**：基于UserId的完整数据隔离机制
- **白标服务**：企业客户可以为终端用户提供品牌化CDN服务
- **资源共享**：共享基础设施，独立计费和管理

### 1.2 核心功能和服务范围

**平台核心功能：**
- CDN加速服务（HTTP/HTTPS/HTTP2/HTTP3）
- 智能DNS解析服务
- Web应用防火墙（WAF）
- DDoS防护
- SSL证书管理
- 实时监控和统计分析
- 缓存管理和优化
- 负载均衡

**服务范围：**
- 静态资源加速
- 动态内容加速
- 视频点播/直播加速
- API加速
- 移动应用加速

### 1.3 技术栈和开发语言

**后端技术栈：**
- **开发语言**：Go 1.21+
- **数据库**：MySQL 5.7+ / TiDB 3.0+
- **通信协议**：gRPC、HTTP/HTTPS、WebSocket
- **缓存系统**：内存缓存 + 文件缓存
- **配置管理**：YAML配置文件
- **日志系统**：结构化日志记录

**前端技术栈：**
- **管理后台**：Go Template + Semantic UI + Vue.js
- **用户前端**：React 18 + TypeScript + TailwindCSS
- **路由管理**：TanStack Router
- **状态管理**：Zustand
- **UI组件**：shadcn/ui

**开发工具：**
- **构建工具**：Go Modules、Vite
- **代码生成**：Protocol Buffers
- **跨平台编译**：支持Linux、macOS、FreeBSD
- **容器化**：Docker支持

## 2. 系统架构设计

### 2.1 整体架构图和分层设计

```
┌─────────────────────────────────────────────────────────────┐
│                    EdgeAPI B2B2C CDN Platform               │
├─────────────────────────────────────────────────────────────┤
│                      Presentation Layer                     │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   EdgeAdmin     │  │    EdgeUser     │  │  REST API    │ │
│  │  (管理后台)      │  │  (用户前端)      │  │   (开放接口)  │ │
│  │  Go + Vue.js    │  │ React + TS      │  │   HTTP API   │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                      Business Layer                         │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                    EdgeAPI                              │ │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐ │ │
│  │  │ User Mgmt   │ │ Server Mgmt │ │   Permission Ctrl   │ │ │
│  │  │ 用户管理     │ │ 服务管理     │ │    权限控制         │ │ │
│  │  └─────────────┘ └─────────────┘ └─────────────────────┘ │ │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐ │ │
│  │  │ Quota Mgmt  │ │ Billing     │ │   Statistics        │ │ │
│  │  │ 配额管理     │ │ 计费系统     │ │    统计分析         │ │ │
│  │  └─────────────┘ └─────────────┘ └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                      Service Layer                          │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                   EdgeNode                              │ │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐ │ │
│  │  │HTTP Server  │ │Cache System │ │   Load Balancer     │ │ │
│  │  │HTTP服务器   │ │ 缓存系统     │ │    负载均衡         │ │ │
│  │  └─────────────┘ └─────────────┘ └─────────────────────┘ │ │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐ │ │
│  │  │WAF/DDoS     │ │SSL/TLS      │ │   Health Check      │ │ │
│  │  │安全防护     │ │ 证书管理     │ │    健康检查         │ │ │
│  │  └─────────────┘ └─────────────┘ └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                      Data Layer                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │     MySQL       │  │   File Cache    │  │ Memory Cache │ │
│  │   主数据库       │  │   文件缓存       │  │   内存缓存    │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    Infrastructure Layer                     │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                  EdgeCommon                             │ │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐ │ │
│  │  │gRPC Client  │ │Config Mgmt  │ │   Utility Funcs     │ │ │
│  │  │RPC通信      │ │ 配置管理     │ │    工具函数         │ │ │
│  │  └─────────────┘ └─────────────┘ └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 各组件职责和交互关系

**EdgeAdmin（管理后台）：**
- 平台管理员操作界面
- 企业用户管理和配置
- 系统监控和运维
- 权限和角色管理
- 通过gRPC与EdgeAPI通信

**EdgeAPI（API节点）：**
- 核心业务逻辑处理
- 数据库操作和事务管理
- 用户认证和权限控制
- 配额管理和计费
- 为EdgeAdmin和EdgeNode提供API服务

**EdgeNode（边缘节点）：**
- HTTP/HTTPS服务器
- 内容缓存和分发
- 负载均衡和故障转移
- 安全防护（WAF/DDoS）
- 实时统计和日志收集

**EdgeUser（用户前端）：**
- 终端用户操作界面
- CDN服务管理
- 统计数据查看
- 通过HTTP API与EdgeAdmin通信

**EdgeCommon（公共组件）：**
- 配置数据结构定义
- gRPC接口定义
- 通用工具函数
- 多语言支持

### 2.3 多租户架构实现方案

**数据隔离机制：**
```go
// 基于UserId的租户隔离
type BaseDAO struct {
    UserId uint32 // 租户标识
}

// 所有数据查询都包含租户过滤
func (dao *ServerDAO) FindServersByUserId(userId uint32) ([]*Server, error) {
    return dao.Query().
        Attr("userId", userId).  // 强制租户隔离
        FindAll()
}
```

**三层权限模型：**
```go
type UserRole string

const (
    RolePlatformAdmin   UserRole = "platform_admin"     // Admin表用户
    RoleEnterpriseAdmin UserRole = "enterprise_admin"   // User表企业用户  
    RoleEndUser         UserRole = "end_user"           // SubUser表终端用户
)
```

**资源层级模型：**
```
Platform Level (平台层)
├── Admin (平台管理员)
├── Shared Node Pool (共享节点池)
└── Platform Services (平台服务)

Enterprise Level (企业层)  
├── User (企业账户)
├── NodeCluster (企业集群)
├── NodeUsageRights (节点使用权)
├── UserQuotas (企业配额)
└── UserAccount (企业计费)

End User Level (终端用户层)
├── SubUser (终端用户)
├── Servers (CDN服务)
├── Domains (加速域名)
└── Statistics (使用统计)
```

### 2.4 权限控制体系设计

**权限控制矩阵：**

| 功能模块 | Platform Admin | Enterprise Admin | End User |
|---------|---------------|------------------|----------|
| 用户管理 | ✅ 所有用户 | ✅ 下级用户 | ❌ |
| 集群管理 | ✅ 所有集群 | ✅ 自有集群 | ❌ |
| 节点管理 | ✅ 所有节点 | ✅ 购买使用权 | ❌ |
| CDN服务 | ✅ 所有服务 | ✅ 企业服务 | ✅ 个人服务 |
| 计费查看 | ✅ 平台账单 | ✅ 企业账单 | ❌ |
| 统计分析 | ✅ 全平台 | ✅ 企业维度 | ✅ 个人维度 |

**权限检查实现：**
```go
func (action *BaseAction) CheckEnterprisePermission() bool {
    if action.IsPlatformAdmin() {
        return true
    }
    
    if !action.IsEnterpriseUser() {
        action.Error("仅企业用户可访问")
        return false
    }
    
    return true
}
```

## 3. 数据模型分析

### 3.1 核心数据表结构和字段定义

**用户管理相关表：**

```sql
-- 管理员表（平台管理员）
CREATE TABLE edgeAdmins (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(100) NOT NULL COMMENT '用户名',
    password VARCHAR(128) NOT NULL COMMENT '密码',
    fullname VARCHAR(100) COMMENT '全名',
    isSuper BOOLEAN DEFAULT FALSE COMMENT '是否超级管理员',
    modules JSON COMMENT '允许的模块',
    canLogin BOOLEAN DEFAULT TRUE COMMENT '是否可登录',
    state TINYINT UNSIGNED DEFAULT 1 COMMENT '状态'
);

-- 企业用户表
CREATE TABLE edgeUsers (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(100) NOT NULL COMMENT '用户名',
    password VARCHAR(128) NOT NULL COMMENT '密码',
    fullname VARCHAR(100) COMMENT '全名',
    businessType ENUM('enterprise', 'individual') DEFAULT 'individual' COMMENT '业务类型',
    enterpriseConfig JSON COMMENT '企业配置',
    clusterId INT UNSIGNED COMMENT '专属集群ID',
    features JSON COMMENT '功能特性',
    state TINYINT UNSIGNED DEFAULT 1 COMMENT '状态'
);

-- 终端用户表
CREATE TABLE edgeSubUsers (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    userId INT UNSIGNED NOT NULL COMMENT '企业用户ID',
    username VARCHAR(100) NOT NULL COMMENT '用户名',
    password VARCHAR(128) NOT NULL COMMENT '密码',
    fullname VARCHAR(100) COMMENT '全名',
    state TINYINT UNSIGNED DEFAULT 1 COMMENT '状态',
    FOREIGN KEY (userId) REFERENCES edgeUsers(id)
);
```

**B2B2C扩展表：**

```sql
-- 节点使用权表（核心新功能）
CREATE TABLE edgeNodeUsageRights (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    userId INT UNSIGNED NOT NULL COMMENT '企业用户ID',
    nodeId INT UNSIGNED NOT NULL COMMENT '节点ID',
    usageType ENUM('bandwidth', 'traffic', 'dedicated') NOT NULL COMMENT '使用类型',
    quota BIGINT UNSIGNED NOT NULL COMMENT '配额',
    usedQuota BIGINT UNSIGNED DEFAULT 0 COMMENT '已使用配额',
    pricePerUnit DECIMAL(10,4) NOT NULL COMMENT '单价',
    startTime BIGINT UNSIGNED NOT NULL COMMENT '开始时间',
    endTime BIGINT UNSIGNED NOT NULL COMMENT '结束时间',
    state TINYINT UNSIGNED DEFAULT 1 COMMENT '状态',
    createdAt BIGINT UNSIGNED NOT NULL COMMENT '创建时间',
    INDEX idx_user_node (userId, nodeId),
    INDEX idx_usage_time (startTime, endTime)
);

-- 企业配额管理表
CREATE TABLE edgeUserQuotas (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    userId INT UNSIGNED NOT NULL COMMENT '企业用户ID',
    quotaType ENUM('bandwidth', 'traffic', 'storage', 'requests') NOT NULL COMMENT '配额类型',
    totalQuota BIGINT UNSIGNED NOT NULL COMMENT '总配额',
    usedQuota BIGINT UNSIGNED DEFAULT 0 COMMENT '已使用配额',
    resetPeriod ENUM('daily', 'monthly', 'yearly') DEFAULT 'monthly' COMMENT '重置周期',
    lastResetAt BIGINT UNSIGNED NOT NULL COMMENT '上次重置时间',
    alertThreshold TINYINT UNSIGNED DEFAULT 80 COMMENT '告警阈值',
    state TINYINT UNSIGNED DEFAULT 1 COMMENT '状态',
    createdAt BIGINT UNSIGNED NOT NULL COMMENT '创建时间',
    UNIQUE KEY uk_user_type (userId, quotaType)
);
```

### 3.2 实体关系图（ERD）

```
Admin (1) ←→ (N) User (企业)
User (企业) (1) ←→ (N) SubUser (终端用户)
User (企业) (1) ←→ (N) NodeUsageRight (节点使用权)
User (企业) (1) ←→ (N) UserQuota (企业配额)
User (企业) (1) ←→ (1) NodeCluster (企业集群)
SubUser (1) ←→ (N) Server (CDN服务)
Server (1) ←→ (N) ServerName (域名)
Server (1) ←→ (N) ServerDailyStat (统计数据)
User (1) ←→ (N) UserAccount (计费账户)
User (1) ←→ (N) UserBill (账单记录)
```

### 3.3 数据隔离机制（基于UserId的租户隔离）

**数据隔离原则：**
- 所有业务表都包含UserId字段作为租户标识
- 所有数据查询都必须包含租户过滤条件
- 通过DAO层统一实现数据隔离逻辑

**实现示例：**
```go
// 数据访问控制
func EnterpriseDataFilter(userId uint32, query string) string {
    // 企业用户只能查看自己的数据
    if strings.Contains(query, "edgeSubUsers") {
        return query + " AND userId = " + strconv.Itoa(int(userId))
    }

    // 终端用户只能查看自己的数据
    if strings.Contains(query, "edgeServers") {
        subUserIds := GetSubUserIds(userId)
        return query + " AND userId IN (" + strings.Join(subUserIds, ",") + ")"
    }

    return query
}
```

### 3.4 B2B2C角色映射关系（Admin → User → SubUser）

**角色层级关系：**
```go
type UserHierarchy struct {
    PlatformAdmin struct {
        AdminId   uint32
        Username  string
        IsSuper   bool
        Modules   []string
    }

    EnterpriseUser struct {
        UserId       uint32
        AdminId      uint32  // 创建该企业用户的管理员ID
        BusinessType string  // "enterprise"
        ClusterId    uint32  // 专属集群ID
        Features     []string
    }

    EndUser struct {
        SubUserId uint32
        UserId    uint32  // 所属企业用户ID
        Username  string
        Permissions []string
    }
}
```

**数据访问权限：**
- **Platform Admin**: 可访问所有数据，无租户限制
- **Enterprise User**: 只能访问自己创建的SubUser和相关数据
- **End User**: 只能访问自己的CDN服务和统计数据

## 4. 业务流程设计

### 4.1 企业客户入驻流程

```mermaid
sequenceDiagram
    participant PA as Platform Admin
    participant API as EdgeAPI
    participant DB as Database
    participant QS as Quota Service

    PA->>API: 创建企业用户
    API->>DB: 插入User记录(businessType='enterprise')
    API->>QS: 初始化默认配额
    QS->>DB: 创建UserQuota记录
    API->>DB: 分配节点使用权
    API-->>PA: 返回企业用户信息

    Note over PA,DB: 企业用户可登录并创建终端用户
```

### 4.2 终端用户服务流程

```mermaid
sequenceDiagram
    participant EU as End User
    participant API as EdgeAPI
    participant QS as Quota Service
    participant Node as EdgeNode

    EU->>API: 创建CDN服务
    API->>QS: 检查企业配额
    QS-->>API: 配额充足
    API->>API: 创建Server记录
    API->>Node: 同步配置
    Node-->>API: 配置成功
    API->>QS: 更新配额使用量
    API-->>EU: 返回服务信息
```

### 4.3 资源分配和配额管理流程

**配额检查逻辑：**
```go
// 配额检查逻辑
func CheckQuota(userId uint32, quotaType string, requestAmount uint64) bool {
    quota := SharedUserQuotaDAO.FindByUserAndType(userId, quotaType)
    if quota == nil {
        return false
    }

    return quota.UsedQuota + requestAmount <= quota.TotalQuota
}

// 使用量更新
func UpdateQuotaUsage(userId uint32, quotaType string, usedAmount uint64) {
    SharedUserQuotaDAO.UpdateUsedQuota(userId, quotaType, usedAmount)

    // 检查告警阈值
    if quota.UsedQuota >= quota.TotalQuota * quota.AlertThreshold / 100 {
        SendQuotaAlert(userId, quotaType)
    }
}
```

### 4.4 计费和统计流程

**计费体系设计：**
```sql
-- 企业账户管理（现有表直接使用）
SELECT * FROM edgeUserAccount WHERE userId = {enterprise_id};

-- 企业账单（现有表直接使用）
SELECT * FROM edgeUserBill WHERE userId = {enterprise_id};

-- 终端用户消费自动计入企业账户
INSERT INTO edgeUserBill (userId, amount, description, createdAt)
SELECT
    su.userId as enterprise_id,
    SUM(cost) as total_cost,
    CONCAT('终端用户:', su.username, 'CDN使用费') as description,
    UNIX_TIMESTAMP() as createdAt
FROM edgeSubUsers su
JOIN edgeServerDailyStats s ON su.id = s.userId
WHERE su.userId = {enterprise_id}
GROUP BY su.userId;
```

## 5. 技术实现细节

### 5.1 API接口设计规范

**RESTful API设计：**
```go
// 企业管理接口
func (this *EnterpriseAction) ManageQuota() {
    userId := this.GetUserId()

    // 检查是否为企业用户
    if !this.IsEnterpriseUser() {
        this.Error("仅企业用户可管理配额")
        return
    }

    quotas := SharedUserQuotaDAO.FindByUserId(userId)
    this.Success(quotas)
}

// 节点使用权购买
func (this *EnterpriseAction) PurchaseNodeUsage() {
    userId := this.GetUserId()
    nodeId := this.ParamInt64("nodeId")
    usageType := this.ParamString("usageType")
    quota := this.ParamInt64("quota")

    // 创建使用权
    usageRight := &edgeconfigs.NodeUsageRight{
        UserId:       userId,
        NodeId:       nodeId,
        UsageType:    usageType,
        Quota:        quota,
        StartTime:    time.Now().Unix(),
        EndTime:      time.Now().AddDate(0, 1, 0).Unix(),
        State:        1,
    }

    SharedNodeUsageRightDAO.CreateUsageRight(usageRight)
    this.Success("购买成功")
}
```

### 5.2 数据访问层（DAO）实现

**DAO层设计模式：**
```go
type UserDAO dbs.DAO

func NewUserDAO() *UserDAO {
    return dbs.NewDAO(&UserDAO{
        DAOObject: dbs.DAOObject{
            DB:     Tea.Env,
            Table:  "edgeUsers",
            Model:  new(User),
            PkName: "id",
        },
    }).(*UserDAO)
}

// 创建用户
func (this *UserDAO) CreateUser(tx *dbs.Tx, username string,
    password string,
    fullname string,
    businessType string) (int64, error) {

    var op = NewUserOperator()
    op.Username = username
    op.Password = stringutil.Md5(password)
    op.Fullname = fullname
    op.BusinessType = businessType
    op.State = UserStateEnabled

    err := this.Save(tx, op)
    if err != nil {
        return 0, err
    }

    return types.Int64(op.Id), nil
}
```

### 5.3 权限验证机制

**权限中间件实现：**
```go
func (this *userMustAuth) BeforeAction(actionPtr actions.ActionWrapper, paramName string) (goNext bool) {
    var action = actionPtr.Object()

    // 检查请求是否合法
    if isEvilRequest(action.Request) {
        action.ResponseWriter.WriteHeader(http.StatusForbidden)
        return false
    }

    // 检测注入
    if !safeFilterRequest(action.Request) {
        action.ResponseWriter.WriteHeader(http.StatusForbidden)
        _, _ = action.ResponseWriter.Write([]byte("Denied By WAF"))
        return false
    }

    // 检查用户权限
    if len(this.module) > 0 && !configloaders.AllowModule(adminId, this.module) {
        action.ResponseWriter.WriteHeader(http.StatusForbidden)
        action.WriteString("Permission Denied.")
        return false
    }

    return true
}
```

### 5.4 配额管理和监控

**配额监控流程：**
```mermaid
flowchart TD
    A[定时任务启动] --> B[查询需要重置的配额]
    B --> C{配额类型}
    C -->|daily| D[重置日配额]
    C -->|monthly| E[重置月配额]
    C -->|yearly| F[重置年配额]
    D --> G[更新lastResetAt]
    E --> G
    F --> G
    G --> H[检查告警阈值]
    H --> I{使用率 >= 阈值?}
    I -->|是| J[发送告警通知]
    I -->|否| K[继续下一个配额]
    J --> K
    K --> L{还有配额?}
    L -->|是| B
    L -->|否| M[任务完成]
```

## 6. 开发和部署

### 6.1 源码编译流程

**编译环境要求：**
- Go 1.21+
- MySQL 5.7+ / TiDB 3.0+
- Node.js 18+ (用于前端编译)
- Protocol Buffers编译器

**编译步骤：**
```bash
# 1. 编译EdgeCommon（公共依赖）
cd EdgeCommon
go mod download
cd build && ./build.sh

# 2. 编译EdgeAPI
cd ../EdgeAPI
go mod download
cp build/configs/api.template.yaml build/configs/api.yaml
cp build/configs/db.template.yaml build/configs/db.yaml
# 修改配置文件
go run -tags community cmd/edge-api/main.go setup
./build/build.sh linux amd64 community

# 3. 编译EdgeAdmin
cd ../EdgeAdmin
go mod download
cp build/configs/server.template.yaml build/configs/server.yaml
cp build/configs/api.template.yaml build/configs/api.yaml
./build/build.sh linux amd64 community

# 4. 编译EdgeNode
cd ../EdgeNode
go mod download
cp build/configs/api_node.template.yaml build/configs/api_node.yaml
./build/build.sh linux amd64 community

# 5. 编译EdgeUser前端
cd ../EdgeUser
pnpm install
pnpm build
```

### 6.2 跨平台编译支持

**支持的平台：**
- Linux (amd64, arm64, 386)
- macOS (amd64, arm64)
- FreeBSD (amd64)
- Windows (amd64) - 部分组件

**交叉编译配置：**
```bash
# macOS上编译Linux版本
CGO_ENABLED=1 ./build/build.sh linux amd64 community

# 编译特定架构
NODE_ARCHITECTS=("amd64" "arm64")
```

### 6.3 配置文件结构

**EdgeAPI配置文件：**
```yaml
# api.yaml
nodeId: "xxx"
secret: "xxx"
rpc:
  listen: "127.0.0.1:8003"

# db.yaml
default:
    db: dev
dbs:
    dev:
        driver: mysql
        dsn: root:123456@tcp(127.0.0.1:3306)/db_edge?charset=utf8mb4
        prefix: edge
```

**EdgeAdmin配置文件：**
```yaml
# server.yaml
http:
  listen: ":7788"

# api.yaml
rpc:
  endpoints: ["127.0.0.1:8003"]
```

### 6.4 常见问题和解决方案

**1. 编译问题：**
```bash
# Go模块下载失败
env https_proxy=127.0.0.1:7890 go mod download

# protoc命令找不到
# Ubuntu: sudo apt install protobuf-compiler
# macOS: brew install protobuf

# CGO相关错误
# Ubuntu: sudo apt install build-essential
# macOS: xcode-select --install
```

**2. 运行时问题：**
```bash
# 数据库连接失败
# 检查db.yaml配置
# 确保MySQL服务运行

# RPC连接失败
# 检查api.yaml中的endpoints配置
# 确保EdgeAPI服务已启动

# 权限问题
# 检查用户角色和模块权限配置
```

**3. 部署建议：**
- 使用systemd管理服务
- 配置日志轮转
- 设置监控告警
- 定期备份数据库
- 使用负载均衡器

**启动顺序：**
1. 启动MySQL服务
2. 启动EdgeAPI节点
3. 启动EdgeAdmin管理平台
4. 通过EdgeAdmin创建并配置EdgeNode节点
5. 启动EdgeNode边缘节点

---

## 总结

EdgeAPI B2B2C多租户CDN平台采用了先进的微服务架构设计，通过完善的多租户隔离机制和权限控制体系，为企业客户提供了完整的CDN服务能力。平台具有以下特点：

1. **架构先进**：采用Go语言开发，支持高并发和分布式部署
2. **多租户支持**：完善的数据隔离和权限控制机制
3. **功能完整**：涵盖CDN加速、安全防护、监控统计等全套功能
4. **扩展性强**：模块化设计，易于扩展和维护
5. **部署灵活**：支持多平台编译和容器化部署

该平台为企业客户提供了强大的CDN服务能力，支持企业为其终端用户提供专业的内容分发服务，是一个成熟的B2B2C商业化CDN解决方案。
