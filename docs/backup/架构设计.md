# EdgeAPI B2B2C多租户CDN平台架构设计

## 1. 项目概述

### 1.1 业务模式转型
为企业客户提供完整的CDN服务能力，使企业客户能够为其终端用户提供CDN加速服务。

### 1.2 角色映射关系
```
B2B2C业务角色
Admin          → Platform Admin (平台管理员)
User           → Enterprise Customer (企业客户)  
SubUser        → End User (终端用户)
```

## 2. 现有架构分析

### 2.1 EdgeAPI架构优势
EdgeAPI已具备完整的多租户基础设施：

| 功能模块 | 现有表结构 | B2B2C适配性 |
|---------|-----------|------------|
| 用户管理 | `Admin` → `User` → `SubUser` | ✅ 天然三层架构 |
| 资源隔离 | 所有表包含`UserId`字段 | ✅ 完美支持租户隔离 |
| 集群管理 | `NodeCluster.UserId` | ✅ 支持企业专属集群 |
| 计费系统 | `UserAccount`、`UserBill`等 | ✅ 完整计费基础 |
| 统计分析 | 丰富的统计表 | ✅ 多维度数据分析 |
| 权限控制 | `UserAccessKey`、`User.Features` | ✅ 细粒度权限管理 |

### 2.2 核心业务流程映射
```
现有流程: Admin创建User → User创建SubUser → SubUser使用CDN
B2B2C流程: 平台管理员 → 企业客户 → 终端用户
```

## 3. 最小化改造方案

### 3.1 数据库变更（仅2个新表）

#### 3.1.1 用户表扩展
```sql
-- 仅需在现有User表添加业务类型标识
ALTER TABLE edgeUsers ADD COLUMN businessType ENUM('enterprise', 'individual') DEFAULT 'individual';
ALTER TABLE edgeUsers ADD COLUMN enterpriseConfig JSON COMMENT '企业配置';
```

#### 3.1.2 节点使用权表（核心新功能）
```sql
CREATE TABLE edgeNodeUsageRights (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    userId INT UNSIGNED NOT NULL COMMENT '企业用户ID',
    nodeId INT UNSIGNED NOT NULL COMMENT '节点ID',
    usageType ENUM('bandwidth', 'traffic', 'dedicated') NOT NULL COMMENT '使用类型',
    quota BIGINT UNSIGNED NOT NULL COMMENT '配额',
    usedQuota BIGINT UNSIGNED DEFAULT 0 COMMENT '已使用配额',
    pricePerUnit DECIMAL(10,4) NOT NULL COMMENT '单价',
    startTime BIGINT UNSIGNED NOT NULL COMMENT '开始时间',
    endTime BIGINT UNSIGNED NOT NULL COMMENT '结束时间',
    state TINYINT UNSIGNED DEFAULT 1 COMMENT '状态',
    createdAt BIGINT UNSIGNED NOT NULL COMMENT '创建时间',
    INDEX idx_user_node (userId, nodeId),
    INDEX idx_usage_time (startTime, endTime)
);
```

#### 3.1.3 企业配额管理表
```sql
CREATE TABLE edgeUserQuotas (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    userId INT UNSIGNED NOT NULL COMMENT '企业用户ID',
    quotaType ENUM('bandwidth', 'traffic', 'storage', 'requests') NOT NULL COMMENT '配额类型',
    totalQuota BIGINT UNSIGNED NOT NULL COMMENT '总配额',
    usedQuota BIGINT UNSIGNED DEFAULT 0 COMMENT '已使用配额',
    resetPeriod ENUM('daily', 'monthly', 'yearly') DEFAULT 'monthly' COMMENT '重置周期',
    lastResetAt BIGINT UNSIGNED NOT NULL COMMENT '上次重置时间',
    alertThreshold TINYINT UNSIGNED DEFAULT 80 COMMENT '告警阈值',
    state TINYINT UNSIGNED DEFAULT 1 COMMENT '状态',
    createdAt BIGINT UNSIGNED NOT NULL COMMENT '创建时间',
    UNIQUE KEY uk_user_type (userId, quotaType)
);
```

### 3.2 现有表充分利用

**无需修改的核心表**：
- `edgeUsers` → 企业客户管理
- `edgeSubUsers` → 终端用户管理
- `edgeNodeClusters` → 企业集群管理
- `edgeUserAccount` → 企业计费账户
- `edgeUserBill` → 企业账单系统
- `edgeServerDailyStats` → 使用统计分析

## 4. 业务架构设计

### 4.1 资源层级模型
```
Platform Level (平台层)
├── Admin (平台管理员)
├── Shared Node Pool (共享节点池)
└── Platform Services (平台服务)

Enterprise Level (企业层)
├── User (企业账户)
├── NodeCluster (企业集群)
├── NodeUsageRights (节点使用权)
├── UserQuotas (企业配额)
└── UserAccount (企业计费)

End User Level (终端用户层)
├── SubUser (终端用户)
├── Servers (CDN服务)
├── Domains (加速域名)
└── Statistics (使用统计)
```

### 4.2 权限体系设计
```go
type UserRole string

const (
    RolePlatformAdmin   UserRole = "platform_admin"     // Admin表用户
    RoleEnterpriseAdmin UserRole = "enterprise_admin"   // User表企业用户
    RoleEndUser         UserRole = "end_user"           // SubUser表终端用户
)

// 基于现有字段的权限判断
func (u *User) GetRole() UserRole {
    if u.AdminId > 0 {
        return RolePlatformAdmin
    }
    if u.BusinessType == "enterprise" {
        return RoleEnterpriseAdmin
    }
    return RoleEndUser
}
```

### 4.3 权限控制矩阵
| 功能模块 | Platform Admin | Enterprise Admin | End User |
|---------|---------------|------------------|----------|
| 用户管理 | ✅ 所有用户 | ✅ 下级用户 | ❌ |
| 集群管理 | ✅ 所有集群 | ✅ 自有集群 | ❌ |
| 节点管理 | ✅ 所有节点 | ✅ 购买使用权 | ❌ |
| CDN服务 | ✅ 所有服务 | ✅ 企业服务 | ✅ 个人服务 |
| 计费查看 | ✅ 平台账单 | ✅ 企业账单 | ❌ |
| 统计分析 | ✅ 全平台 | ✅ 企业维度 | ✅ 个人维度 |

## 5. 核心业务流程

### 5.1 企业客户入驻流程
```
1. Platform Admin创建企业User账户
   ↓
2. 设置businessType='enterprise'
   ↓  
3. 配置企业初始配额(UserQuotas)
   ↓
4. 分配节点使用权(NodeUsageRights)
   ↓
5. 企业Admin登录创建集群
   ↓
6. 创建终端用户账户(SubUser)
```

### 5.2 终端用户服务流程
```
1. End User登录(SubUser账户)
   ↓
2. 创建CDN服务(Server)
   ↓
3. 配置加速域名(ServerNames)
   ↓
4. 部署到企业集群(NodeCluster)
   ↓
5. 使用量计入企业账户(UserAccount)
```

### 5.3 配额控制流程
```go
// 配额检查逻辑
func CheckQuota(userId uint32, quotaType string, requestAmount uint64) bool {
    quota := SharedUserQuotaDAO.FindByUserAndType(userId, quotaType)
    if quota == nil {
        return false
    }
    
    return quota.UsedQuota + requestAmount <= quota.TotalQuota
}

// 使用量更新
func UpdateQuotaUsage(userId uint32, quotaType string, usedAmount uint64) {
    SharedUserQuotaDAO.UpdateUsedQuota(userId, quotaType, usedAmount)
    
    // 检查告警阈值
    if quota.UsedQuota >= quota.TotalQuota * quota.AlertThreshold / 100 {
        SendQuotaAlert(userId, quotaType)
    }
}
```

## 6. 计费体系设计

### 6.1 利用现有计费架构
```sql
-- 企业账户管理（现有表直接使用）
SELECT * FROM edgeUserAccount WHERE userId = {enterprise_id};

-- 企业账单（现有表直接使用）
SELECT * FROM edgeUserBill WHERE userId = {enterprise_id};

-- 终端用户消费自动计入企业账户
INSERT INTO edgeUserBill (userId, amount, description, createdAt)
SELECT 
    su.userId as enterprise_id,
    SUM(cost) as total_cost,
    CONCAT('终端用户:', su.username, 'CDN使用费') as description,
    UNIX_TIMESTAMP() as createdAt
FROM edgeSubUsers su
JOIN edgeServerDailyStats s ON su.id = s.userId
WHERE su.userId = {enterprise_id}
GROUP BY su.userId;
```

### 6.2 节点使用权计费
```sql
-- 节点使用权费用计算
SELECT 
    nur.userId,
    nur.nodeId,
    nur.usedQuota,
    nur.pricePerUnit,
    nur.usedQuota * nur.pricePerUnit as cost
FROM edgeNodeUsageRights nur
WHERE nur.state = 1
    AND nur.startTime <= UNIX_TIMESTAMP()
    AND nur.endTime >= UNIX_TIMESTAMP();
```

## 7. 统计分析设计

### 7.1 企业维度统计视图
```sql
-- 企业流量统计
CREATE VIEW v_enterprise_traffic_stats AS
SELECT 
    u.id as enterprise_id,
    u.fullname as enterprise_name,
    DATE(FROM_UNIXTIME(s.createdAt)) as stat_date,
    SUM(s.bytes) as total_traffic,
    SUM(s.countRequests) as total_requests,
    SUM(s.countCachedRequests) as cached_requests,
    ROUND(SUM(s.countCachedRequests) * 100.0 / SUM(s.countRequests), 2) as cache_hit_rate
FROM edgeUsers u
LEFT JOIN edgeSubUsers su ON u.id = su.userId
LEFT JOIN edgeServerDailyStats s ON su.id = s.userId
WHERE u.businessType = 'enterprise'
GROUP BY u.id, DATE(FROM_UNIXTIME(s.createdAt));

-- 企业用户使用排行
CREATE VIEW v_enterprise_user_ranking AS
SELECT 
    u.id as enterprise_id,
    su.id as user_id,
    su.username,
    SUM(s.bytes) as total_traffic,
    SUM(s.countRequests) as total_requests,
    AVG(s.countCachedRequests * 100.0 / s.countRequests) as avg_cache_hit_rate
FROM edgeUsers u
JOIN edgeSubUsers su ON u.id = su.userId
JOIN edgeServerDailyStats s ON su.id = s.userId
WHERE u.businessType = 'enterprise'
GROUP BY u.id, su.id
ORDER BY total_traffic DESC;
```

### 7.2 配额使用监控
```sql
-- 配额使用情况监控
SELECT 
    u.fullname as enterprise_name,
    uq.quotaType,
    uq.totalQuota,
    uq.usedQuota,
    ROUND(uq.usedQuota * 100.0 / uq.totalQuota, 2) as usage_percentage,
    CASE 
        WHEN uq.usedQuota >= uq.totalQuota * uq.alertThreshold / 100 THEN 'ALERT'
        WHEN uq.usedQuota >= uq.totalQuota * 0.8 THEN 'WARNING'
        ELSE 'NORMAL'
    END as status
FROM edgeUsers u
JOIN edgeUserQuotas uq ON u.id = uq.userId
WHERE u.businessType = 'enterprise'
ORDER BY usage_percentage DESC;
```

## 8. API设计

### 8.1 现有API保持兼容
```go
// 现有API完全保持不变
func (this *UserAction) CreateUser() {
    // 原有逻辑完全保持
}

func (this *ServerAction) CreateServer() {
    // 原有逻辑完全保持
}
```

### 8.2 新增企业管理API
```go
// 企业配额管理
func (this *EnterpriseAction) ManageQuota() {
    userId := this.GetUserId()
    
    // 检查是否为企业用户
    if !this.IsEnterpriseUser() {
        this.Error("仅企业用户可管理配额")
        return
    }
    
    quotas := SharedUserQuotaDAO.FindByUserId(userId)
    this.Success(quotas)
}

// 节点使用权购买
func (this *EnterpriseAction) PurchaseNodeUsage() {
    userId := this.GetUserId()
    nodeId := this.ParamInt64("nodeId")
    usageType := this.ParamString("usageType")
    quota := this.ParamInt64("quota")
    
    // 创建使用权
    usageRight := &edgeconfigs.NodeUsageRight{
        UserId:       userId,
        NodeId:       nodeId,
        UsageType:    usageType,
        Quota:        quota,
        StartTime:    time.Now().Unix(),
        EndTime:      time.Now().AddDate(0, 1, 0).Unix(),
        State:        1,
    }
    
    SharedNodeUsageRightDAO.CreateUsageRight(usageRight)
    this.Success("购买成功")
}
```

## 9. 实施计划

### 9.1 开发阶段划分

#### 第一阶段：基础架构 (2周)
- [ ] 数据库表结构创建
- [ ] 用户类型扩展
- [ ] 基础权限控制
- [ ] 配额管理功能

#### 第二阶段：企业功能 (3周)
- [ ] 企业用户管理界面
- [ ] 节点使用权管理
- [ ] 配额监控告警
- [ ] 企业统计报表

#### 第三阶段：终端用户 (2周)
- [ ] 终端用户CDN创建
- [ ] 使用限制控制
- [ ] 个人统计界面
- [ ] 服务监控

#### 第四阶段：优化完善 (1周)
- [ ] 性能优化
- [ ] 监控完善
- [ ] 文档完善
- [ ] 测试验证

### 9.2 风险控制
- **向后兼容**：所有现有功能保持不变
- **渐进上线**：分阶段发布，降低风险
- **回滚机制**：可快速回滚到现有版本
- **数据安全**：新增表不影响现有数据

## 10. 技术要点

### 10.1 数据访问控制
```go
// 基于现有架构的数据过滤
func EnterpriseDataFilter(userId uint32, query string) string {
    // 企业用户只能查看自己的数据
    if strings.Contains(query, "edgeSubUsers") {
        return query + " AND userId = " + strconv.Itoa(int(userId))
    }
    
    // 终端用户只能查看自己的数据
    if strings.Contains(query, "edgeServers") {
        subUserIds := GetSubUserIds(userId)
        return query + " AND userId IN (" + strings.Join(subUserIds, ",") + ")"
    }
    
    return query
}
```

### 10.2 缓存策略
```go
// 利用现有缓存机制
func GetEnterpriseStats(userId uint32) *EnterpriseStats {
    cacheKey := fmt.Sprintf("enterprise_stats_%d", userId)
    
    if stats := cache.Get(cacheKey); stats != nil {
        return stats.(*EnterpriseStats)
    }
    
    stats := CalculateEnterpriseStats(userId)
    cache.Set(cacheKey, stats, time.Hour)
    return stats
}
```

