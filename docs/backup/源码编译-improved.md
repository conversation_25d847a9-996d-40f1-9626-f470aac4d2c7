# 源码编译指南

要想从源码运行或者编译GoEdge，需要分别编译EdgeAdmin、EdgeAPI、EdgeNode等组件。本指南将详细介绍编译环境准备、源码获取、编译流程以及常见问题解决方案。

## 源码获取

当前文档中的"下载"可以使用两种方式：
1. 直接从GitHub下载zip压缩包
2. 使用git clone命令克隆仓库（推荐，更易于维护和更新）

```bash
# 创建项目根目录
mkdir -p EdgeProject
cd EdgeProject

# 克隆各组件源码
git clone https://github.com/TeaOSLab/EdgeCommon.git
git clone https://github.com/TeaOSLab/EdgeAdmin.git
git clone https://github.com/TeaOSLab/EdgeAPI.git
git clone https://github.com/TeaOSLab/EdgeNode.git
```

## 整体源码结构

从Github上对应仓库下载各个组件的源码后，建议的整体源码结构为：

```
EdgeProject/
   EdgeAdmin/     # 管理平台
   EdgeAPI/       # API节点
   EdgeNode/      # 边缘节点
   EdgeCommon/    # 公共依赖
```

源码下载地址：
- GitHub: https://github.com/TeaOSLab （官方仓库，一直保持最新）

## 运行环境要求

### 基本环境
- **操作系统**：目前只支持macOS和Linux开发环境；FreeBSD下可以编译EdgeNode
- **MySQL**：支持 v5.7.x 以上 / TiDB 3.0以上；
- **Golang**：支持 v1.21及以上；
- **其他工具**：git、zip、unzip、bash

### 平台特定要求
- **macOS**：需要安装musl交叉编译工具链，用于编译Linux版本的边缘节点
- **Linux**：需要安装相应的开发工具包
- **FreeBSD**：需要安装bash和zip工具

## 安装必要的依赖

### Go环境设置
首先确保您的系统已经安装了Go 1.21或更高版本：

```bash
# 检查Go版本
go version

# 如果没有安装或版本过低，请安装或升级
# 以Ubuntu为例
# sudo apt update
# sudo apt install golang-1.21
```

确保设置了正确的GOPATH环境变量：

```bash
export GOPATH=$HOME/go
export PATH=$PATH:$GOPATH/bin
```

### 安装musl交叉编译工具链
由于边缘节点启用了CGO_ENABLED，所以如果你是在macOS X上交叉编译Linux上的边缘节点，需要先在系统中安装musl库：

```bash
brew install FiloSottile/musl-cross/musl-cross --with-x86_64 --with-aarch64 --with-arm-hf --with-i486 --with-arm --with-mips --with-mipsel --with-mips64 --with-mips64el
```

这个安装过程最长可能需要几个小时，需要耐心等待。

如果在安装过程中总是提示下载失败，可以使用一个代理：

```bash
export ALL_PROXY="127.0.0.1:7890"
```

其中 127.0.0.1:7890 换成你自己的代理地址。

如果已经安装过但是支持的平台或架构不全，可以卸载后再安装：

```bash
brew uninstall FiloSottile/musl-cross/musl-cross
brew install FiloSottile/musl-cross/musl-cross --with-x86_64 --with-aarch64 --with-arm-hf --with-i486 --with-arm --with-mips --with-mipsel --with-mips64 --with-mips64el
```

安装完成后，可以在 /usr/local/bin 和 /usr/local/opt/musl-cross/bin 下找到对应的文件。

## 设置MySQL数据库
GoEdge项目需要MySQL数据库。您可以使用Docker快速设置一个MySQL实例用于开发：

```bash
# 启动一个MySQL 5.7容器
docker run --name mysql57 -e MYSQL_ROOT_PASSWORD=123456 -e MYSQL_DATABASE=db_edge -p 3306:3306 -d mysql:5.7 --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci
```

或者使用项目根目录下提供的`start-mysql.sh`脚本：

```bash
chmod +x ./start-mysql.sh
./start-mysql.sh
```

## 编译各个组件

### 编译EdgeCommon（公共依赖）
EdgeCommon是各个组件公共依赖的源码，需要首先编译：

1. 从 https://github.com/TeaOSLab/EdgeCommon 下载EdgeCommon源码
2. 转到 EdgeCommon 目录
3. 执行 `go mod download` 下载项目依赖
4. 执行 `cd build && ./build.sh` 生成protobuf相关文件

### 编译EdgeAdmin管理平台

1. 从 https://github.com/TeaOSLab/EdgeAdmin 下载EdgeAdmin源码；
2. 从 https://github.com/TeaOSLab/EdgeCommon 下载EdgeCommon源码，如果已经下载则不需要重复下载；
3. 将EdgeAdmin和EdgeCommon放在同一目录下；
4. 转到 EdgeAdmin 目录下；
5. 执行 `go mod download` 下载项目依赖的源码；
   ```bash
   # 如果下载失败，可以使用代理
   env https_proxy=127.0.0.1:7890 go mod download
   ```
6. 复制 build/configs/server.template.yaml 到 build/configs/server.yaml，如果 server.yaml 已经存在则无需重复复制；
   ```bash
   cp build/configs/server.template.yaml build/configs/server.yaml
   ```
   这个文件里默认指定了管理平台的访问端口为7788，可以根据自己的需要进行修改；
   
7. 复制 build/configs/api.template.yaml 到 build/configs/api.yaml；
   ```bash
   cp build/configs/api.template.yaml build/configs/api.yaml
   ```
   这个文件默认指定了API节点的端口为8003，在启动API节点时如果修改了端口号，也要在这里进行同步的修改；

8. 运行开源版本：
   ```bash
   go run -tags community cmd/edge-admin/main.go
   ```
   商业版源码请将 -tags community 换成 -tags plus

9. 如果想编译整个项目，请执行：
   ```bash
   ./build/build.sh linux amd64 community
   ```
   编译后生成的压缩包可以在dist目录下找到。

### 编译EdgeAPI API节点

API节点是唯一可以操作数据库的节点，所以需要在步骤中配置数据库，也是其他节点依赖运行的节点。

1. 从 https://github.com/TeaOSLab/EdgeAPI 下载EdgeAPI源码；
2. 从 https://github.com/TeaOSLab/EdgeCommon 下载EdgeCommon源码，如果已经下载则不需要重复下载；
3. 将EdgeAPI和EdgeCommon放在同一目录下；
4. 转到 EdgeAPI 目录下；
5. 执行 `go mod download` 下载项目依赖的源码；
   ```bash
   # 如果下载失败，可以使用代理
   env https_proxy=127.0.0.1:7890 go mod download
   ```
   
6. 复制 build/configs/api.template.yaml 到 build/configs/api.yaml，如果 api.yaml 已经存在则无需重复复制；
   ```bash
   cp build/configs/api.template.yaml build/configs/api.yaml
   ```
   然后修改其中的配置 nodeId 为API节点的ID，secret 为API节点的密钥；如果还没有创建过API节点，则可以在修改数据库配置后，通过执行初始化命令来设置：
   ```bash
   go run -tags community cmd/edge-api/main.go setup -api-node-protocol=http -api-node-host=127.0.0.1 -api-node-port=8003
   ```
   初始化完成后，可以从控制台提示或者数据库 edgeAPINodes 表中获取节点ID（字段uniqueId）和密钥（字段secret）；
   
7. 复制 build/configs/db.template.yaml 到 build/configs/db.yaml，将其中的 prod 修改为 dev，并修改其中的数据库配置：
   ```bash
   cp build/configs/db.template.yaml build/configs/db.yaml
   ```
   编辑 db.yaml 文件，设置正确的数据库连接信息（用户名、密码、主机和端口）：
   ```yaml
   user: root
   password: 123456
   host: 127.0.0.1:3306
   database: db_edge
   ```
   
8. 运行开源版本：
   ```bash
   go run -tags community cmd/edge-api/main.go
   ```
   商业版源码请将 -tags community 换成 -tags plus
   
9. 如果想编译整个项目，请执行：
   ```bash
   ./build/build.sh linux amd64 community
   ```
   编译后生成的压缩包可以在dist目录下找到。

#### 自动集成数据库结构变更
如果你修改了数据库结构，希望用户在安装时自动升级老的数据库，你需要运行 build/sql.sh 脚本，自动从你的数据库中生成新的结构代码（internal/setup/sql.go文件），然后再运行build.sh来重新编译API节点：

```bash
cd build
./sql.sh
./build.sh linux amd64 community
```

对于开源版本，如果你想每次运行编译脚本的时候都自动运行sql.sh，可以修改build.sh中的：

```bash
if [ $TAG = "plus" ]; then
    echo "building sql ..."
    ${ROOT}/sql.sh
fi
```

修改为：
```bash
if [ $TAG = "community" ]; then
    echo "building sql ..."
    ${ROOT}/sql.sh
fi
```

### 编译EdgeNode边缘节点

1. 从 https://github.com/TeaOSLab/EdgeNode 下载EdgeNode源码；
2. 从 https://github.com/TeaOSLab/EdgeCommon 下载EdgeCommon源码，如果已经下载则不需要重复下载；
3. 将EdgeNode和EdgeCommon放在同一目录下；
4. 转到 EdgeNode 目录下；
5. 执行 `go mod download` 下载项目依赖的源码；
   ```bash
   # 如果下载失败，可以使用代理
   env https_proxy=127.0.0.1:7890 go mod download
   ```
   
6. 复制 build/configs/api_node.template.yaml 到 build/configs/api_node.yaml，如果已存在则无需复制：
   ```bash
   cp build/configs/api_node.template.yaml build/configs/api_node.yaml
   ```
   然后修改其中的配置；如果你还没有边缘节点，需要先运行EdgeAdmin并通过界面创建一个节点后再修改配置；
   
7. 运行开源版本：
   ```bash
   go run -tags community cmd/edge-node/main.go
   ```
   商业版源码请将 -tags community 换成 -tags plus
   
8. 如果想编译整个项目，请执行：
   ```bash
   ./build/build.sh linux amd64 community
   ```
   编译后生成的压缩包可以在dist目录下找到。

## 跨平台编译

### Linux上编译
在Linux平台上，可以直接编译各个组件：

```bash
cd EdgeAdmin
./build/build.sh linux amd64 community

cd ../EdgeAPI
./build/build.sh linux amd64 community

cd ../EdgeNode
./build/build.sh linux amd64 community
```

### macOS上编译
在macOS上进行交叉编译时，特别是编译EdgeNode组件，需要确保已安装musl-cross工具链：

```bash
cd EdgeAdmin
./build/build.sh linux amd64 community

cd ../EdgeAPI
./build/build.sh linux amd64 community

cd ../EdgeNode
CGO_ENABLED=1 ./build/build.sh linux amd64 community
```

### FreeBSD上编译EdgeNode

在FreeBSD系统上编译边缘节点，首先确保bash命令存在：

```bash
bash
```

如果提示 `bash: Command not found` 则可以使用pkg命令安装：
```bash
pkg install bash
```

检查zip是否存在，如果不存在同样安装：

```bash
pkg install zip
```

然后可以在FreeBSD系统中直接编译（需要先下载源码EdgeCommon和EdgeNode）：

```bash
./build.sh freebsd amd64
```

## 常见问题

### 下载依赖的Go模块失败
go mod download操作可能因为网络原因失败，如果失败，建议使用网络代理尝试重新下载：

```bash
env https_proxy=127.0.0.1:7890 go mod download
```

其中的 127.0.0.1:7890 换成你自己的网络代理地址。

### 编译特定架构的边缘节点
如果出现amd64之外的节点编译报错时，可以修改EdgeAPI/build/build.sh和EdgeNode/build/build.sh脚本，修改其中的架构列表：

```bash
NODE_ARCHITECTS=("amd64" "386" "arm64" "mips64" "mips64le")
```

修改为仅包含你需要的架构，例如：
```bash
NODE_ARCHITECTS=("amd64")
```

这样只编译amd64架构，适合大部分Linux系统。

### 常见编译错误

1. **找不到protoc命令**：
   ```
   # 在Ubuntu上安装
   sudo apt install protobuf-compiler
   # 在macOS上安装
   brew install protobuf
   ```

2. **找不到Go的protobuf插件**：
   ```bash
   go install google.golang.org/protobuf/cmd/protoc-gen-go@latest
   go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest
   ```

3. **CGO相关错误**：
   确保已安装gcc或其他C编译器
   ```bash
   # Ubuntu
   sudo apt install build-essential
   # macOS
   xcode-select --install
   ```

## 参考资料

### Linux上使用musl-cross-make
安装前需要准备wget、patch、bzip2、gcc-c++等。

```bash
# Ubuntu/Debian
sudo apt install wget patch bzip2 g++

# CentOS/RHEL
sudo yum install wget patch bzip2 gcc-c++
```

### 完整启动顺序
正确的启动顺序为：

1. 启动MySQL服务
2. 启动EdgeAPI节点
3. 启动EdgeAdmin管理平台
4. 通过EdgeAdmin创建并配置EdgeNode节点
5. 启动EdgeNode边缘节点
